"""
Custom neural network layers for Crystal VAE.

This module contains specialized neural network layers for E(3)-equivariant
graph neural networks and capsule networks used in crystal structure modeling.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from e3nn import o3
from e3nn.o3 import FullyConnectedTensorProduct, spherical_harmonics
from torch_geometric.utils import scatter


class RadialBasisLayer(nn.Module):
    """
    Radial basis function layer for encoding distances.
    
    This layer converts scalar distances into a vector of radial basis functions,
    providing a smooth representation of distance information for the neural network.
    """
    
    def __init__(self, num_rbf, cutoff):
        """
        Initialize the radial basis layer.
        
        Args:
            num_rbf (int): Number of radial basis functions
            cutoff (float): Maximum distance cutoff
        """
        super().__init__()
        self.num_rbf = num_rbf
        self.cutoff = cutoff

        # Initialize centers and widths as non-trainable parameters
        self.centers = nn.Parameter(torch.linspace(0, cutoff, num_rbf), requires_grad=False)
        self.widths = nn.Parameter((cutoff / num_rbf) * torch.ones(num_rbf), requires_grad=False)

    def forward(self, dist):
        """
        Apply radial basis functions to distances.
        
        Args:
            dist (torch.Tensor): Distance tensor with shape [num_edges, 1]
            
        Returns:
            torch.Tensor: RBF-encoded distances with shape [num_edges, num_rbf]
        """
        # Apply cutoff
        dist = dist.clamp(max=self.cutoff)

        # Expand distances for vectorized computation
        dist_expanded = dist.expand(-1, self.num_rbf)
        
        # Compute Gaussian RBFs
        rbf = torch.exp(-((dist_expanded - self.centers.view(1, -1)) / self.widths.view(1, -1))**2)

        # Apply smooth envelope function
        envelope = self._envelope(dist)
        envelope_expanded = envelope.expand_as(rbf)
        
        return rbf * envelope_expanded

    def _envelope(self, dist):
        """
        Smooth envelope function for distance cutoff.
        
        Args:
            dist (torch.Tensor): Distance tensor
            
        Returns:
            torch.Tensor: Envelope values
        """
        return 1 - (dist / self.cutoff)**2


class E3EquivariantCGCNNConv(nn.Module):
    """
    E(3)-equivariant Crystal Graph Convolutional Neural Network layer.
    
    This layer performs message passing while maintaining equivariance to 3D rotations,
    making it suitable for crystal structure analysis where rotational symmetry is important.
    """
    
    def __init__(self, channels, num_rbf=16, cutoff=10.0, lmax=1):
        """
        Initialize the E(3)-equivariant convolution layer.
        
        Args:
            channels (int): Number of feature channels
            num_rbf (int): Number of radial basis functions
            cutoff (float): Distance cutoff for interactions
            lmax (int): Maximum spherical harmonic degree
        """
        super().__init__()
        self.channels = channels
        self.num_rbf = num_rbf
        self.cutoff = cutoff
        self.lmax = lmax

        # Radial basis functions (invariant to rotations)
        self.rbf = RadialBasisLayer(num_rbf, cutoff)

        # Scalar network (invariant)
        self.scalar_mlp = nn.Sequential(
            nn.Linear(channels * 2 + num_rbf, channels),
            nn.SiLU(),
            nn.Linear(channels, channels)
        )

        # Define irreducible representations
        irreps_in1 = o3.Irreps(f"{channels}x0e + {channels}x1e")
        irreps_in2 = o3.Irreps([(1, (l, 1)) for l in range(lmax + 1)])
        irreps_out = o3.Irreps(f"{channels}x0e + {channels}x1e")

        # Tensor product for equivariant message passing
        self.tp = FullyConnectedTensorProduct(
            irreps_in1=irreps_in1,
            irreps_in2=irreps_in2,
            irreps_out=irreps_out,
            internal_weights=True
        )

        # Activation functions
        self.scalar_act = nn.SiLU()
        self.gate_act = nn.Sigmoid()

    def forward(self, x_scalar, x_vector, edge_index, edge_attr, pos):
        """
        Forward pass through the E(3)-equivariant convolution.
        
        Args:
            x_scalar (torch.Tensor): Scalar node features [num_nodes, channels]
            x_vector (torch.Tensor): Vector node features [num_nodes, channels, 3]
            edge_index (torch.Tensor): Edge connectivity [2, num_edges]
            edge_attr (torch.Tensor): Edge attributes [num_edges, edge_features]
            pos (torch.Tensor): Node positions [num_nodes, 3]
            
        Returns:
            tuple: Updated (x_scalar, x_vector) features
        """
        row, col = edge_index

        # Calculate edge vectors and distances (invariant)
        edge_vec = pos[row] - pos[col]
        dist = torch.norm(edge_vec, dim=-1, keepdim=True)

        # Get radial basis functions
        rbf_output = self.rbf(dist)

        # Scalar message computation
        scalar_message_input = torch.cat([
            x_scalar[row],
            x_scalar[col],
            rbf_output
        ], dim=-1)

        scalar_message = self.scalar_mlp(scalar_message_input)

        # Calculate spherical harmonics for equivariant features
        edge_sh = spherical_harmonics(
            list(range(self.lmax + 1)),
            edge_vec / (dist + 1e-8),
            normalize=True
        )

        # Combine scalar and vector features for tensor product
        x_vector_flat = x_vector[row].reshape(x_vector[row].shape[0], -1)
        src_features = torch.cat([x_scalar[row], x_vector_flat], dim=-1)

        # Apply tensor product for equivariant message passing
        message = self.tp(src_features, edge_sh)

        # Split message back into scalar and vector parts
        message_scalar = message[:, :self.channels]
        message_vector = message[:, self.channels:].view(-1, self.channels, 3)

        # Aggregate messages
        scalar_out = scatter(scalar_message, col, dim=0, dim_size=x_scalar.size(0), reduce='add')
        vector_out = scatter(message_vector, col, dim=0, dim_size=x_vector.size(0), reduce='add')

        # Apply activations
        scalar_out = self.scalar_act(scalar_out)

        # Apply gating to vectors (equivariant)
        gates = self.gate_act(scalar_out)
        gated_vectors = vector_out * gates.unsqueeze(-1)

        # Update features with residual connections
        x_scalar_new = x_scalar + scalar_out
        x_vector_new = x_vector + gated_vectors

        return x_scalar_new, x_vector_new


class E3EquivariantPrimaryCapsuleLayer(nn.Module):
    """
    E(3)-equivariant primary capsule layer.
    
    This layer converts scalar and vector features into capsule representations
    while maintaining rotational equivariance.
    """
    
    def __init__(self, scalar_features, vector_features, out_caps, caps_dim):
        """
        Initialize the primary capsule layer.
        
        Args:
            scalar_features (int): Number of input scalar features
            vector_features (int): Number of input vector features
            out_caps (int): Number of output capsules
            caps_dim (int): Dimension of each capsule
        """
        super().__init__()
        self.out_caps = out_caps
        self.caps_dim = caps_dim
        self.scalar_features = scalar_features
        self.vector_features = vector_features

        # Scalar projection (invariant)
        self.scalar_projection = nn.Linear(scalar_features, out_caps * (caps_dim // 2))

        # Vector projection weights (invariant)
        self.vector_weights = nn.Linear(scalar_features, out_caps * vector_features)

        # Vector to scalar transformation
        self.vector_to_scalar = nn.Linear(vector_features * 3, caps_dim // 2)

    def forward(self, x_scalar, x_vector):
        """
        Forward pass through primary capsule layer.
        
        Args:
            x_scalar (torch.Tensor): Scalar features [batch_size, scalar_features]
            x_vector (torch.Tensor): Vector features [batch_size, vector_features, 3]
            
        Returns:
            tuple: (capsules, x_vector) where capsules has shape [batch_size, out_caps, caps_dim]
        """
        batch_size = x_scalar.size(0)

        # Project scalar features (invariant)
        scalar_out = self.scalar_projection(x_scalar)
        scalar_out = scalar_out.view(batch_size, self.out_caps, self.caps_dim // 2)

        # Get weights for vector features
        vector_weights = self.vector_weights(x_scalar)
        vector_weights = vector_weights.view(batch_size, self.out_caps, self.vector_features)

        # Process vectors for each capsule
        vector_caps = []
        for i in range(self.out_caps):
            # Weight the vectors with proper broadcasting
            weights_i = vector_weights[:, i, :].unsqueeze(-1)  # [batch_size, vector_features, 1]
            weighted_vectors = weights_i * x_vector  # [batch_size, vector_features, 3]

            # Flatten and convert to scalars
            weighted_flat = weighted_vectors.reshape(batch_size, -1)
            vector_scalars = self.vector_to_scalar(weighted_flat)

            vector_caps.append(vector_scalars)

        # Stack to get [batch_size, out_caps, caps_dim//2]
        vector_caps = torch.stack(vector_caps, dim=1)

        # Concatenate scalar and vector parts
        capsules = torch.cat([scalar_out, vector_caps], dim=2)

        return capsules, x_vector


class E3EquivariantSecondaryCapsuleLayer(nn.Module):
    """
    E(3)-equivariant secondary capsule layer with dynamic routing.
    
    This layer implements the dynamic routing algorithm for capsule networks
    while maintaining rotational equivariance.
    """
    
    def __init__(self, in_dim, out_caps, out_dim, routing_iterations=2):
        """
        Initialize the secondary capsule layer.
        
        Args:
            in_dim (int): Input capsule dimension
            out_caps (int): Number of output capsules
            out_dim (int): Output capsule dimension
            routing_iterations (int): Number of routing iterations
        """
        super().__init__()
        self.out_caps = out_caps
        self.out_dim = out_dim
        self.routing_iterations = routing_iterations
        self.in_dim = in_dim

        # Transform matrices (invariant)
        self.W = nn.Parameter(torch.randn(out_caps, in_dim, out_dim))
        self.bias = nn.Parameter(torch.zeros(out_caps, out_dim))

    def squash(self, tensor, dim=-1):
        """
        Squashing function for capsule activations.
        
        Args:
            tensor (torch.Tensor): Input tensor
            dim (int): Dimension along which to apply squashing
            
        Returns:
            torch.Tensor: Squashed tensor
        """
        squared_norm = (tensor ** 2).sum(dim=dim, keepdim=True)
        scale = squared_norm / (1 + squared_norm)
        return scale * tensor / (torch.sqrt(squared_norm) + 1e-8)

    def forward(self, x, x_vectors, batch):
        """
        Forward pass through secondary capsule layer.
        
        Args:
            x (torch.Tensor): Primary capsules [num_nodes, primary_caps, primary_dim]
            x_vectors (torch.Tensor): Vector features [num_nodes, vector_features, 3]
            batch (torch.Tensor): Batch indices for nodes
            
        Returns:
            tuple: (secondary_capsules, secondary_vectors)
        """
        batch_size = batch.max().item() + 1
        secondary_capsules = []
        secondary_vectors = []

        for b in range(batch_size):
            mask = (batch == b)
            if mask.sum() == 0:
                continue

            x_b = x[mask]  # [nodes, primary_caps, primary_dim]
            nodes, primary_caps, primary_dim = x_b.size()

            # Prepare prediction vectors
            u_hat = torch.zeros(nodes, primary_caps, self.out_caps, self.out_dim, device=x.device)

            # Transform inputs for each output capsule
            for i in range(self.out_caps):
                u_hat[:, :, i, :] = torch.matmul(x_b, self.W[i])

            # Flatten for routing
            u_hat_flat = u_hat.view(-1, self.out_caps, self.out_dim)
            num_inputs = u_hat_flat.size(0)

            # Initialize routing logits
            b_ij = torch.zeros(num_inputs, self.out_caps, device=x.device)

            # Dynamic routing algorithm
            for iteration in range(self.routing_iterations):
                c_ij = F.softmax(b_ij, dim=1)
                c_ij = c_ij.unsqueeze(2)

                # Weighted sum
                s_j = (c_ij * u_hat_flat).sum(dim=0) + self.bias

                # Apply squashing
                v_j = self.squash(s_j, dim=1)

                # Update routing logits
                if iteration < self.routing_iterations - 1:
                    agreement = (u_hat_flat * v_j.unsqueeze(0)).sum(dim=2)
                    b_ij = b_ij + agreement

            # Handle vectors if provided
            if x_vectors is not None:
                # Simple aggregation for vectors
                routed_vectors = torch.zeros(self.out_caps, x_vectors.size(1), 3, device=x.device)
                secondary_vectors.append(routed_vectors.unsqueeze(0))

            secondary_capsules.append(v_j.unsqueeze(0))

        if not secondary_capsules:
            return torch.zeros((batch_size, self.out_caps, self.out_dim), device=x.device), None

        return torch.cat(secondary_capsules, dim=0), torch.cat(secondary_vectors, dim=0) if secondary_vectors else None


class E3LayerNorm(nn.Module):
    """
    E(3)-equivariant layer normalization.
    
    This layer normalizes scalar and vector features while preserving
    rotational equivariance for the vector components.
    """
    
    def __init__(self, channels, scalar_only=False):
        """
        Initialize E(3)-equivariant layer normalization.
        
        Args:
            channels (int): Number of feature channels
            scalar_only (bool): Whether to normalize only scalar features
        """
        super().__init__()
        self.channels = channels
        self.scalar_only = scalar_only

        self.scalar_scale = nn.Parameter(torch.ones(channels))
        self.scalar_bias = nn.Parameter(torch.zeros(channels))

        if not scalar_only:
            self.vector_scale = nn.Parameter(torch.ones(channels))

    def forward(self, x_scalar, x_vector=None):
        """
        Apply E(3)-equivariant normalization.
        
        Args:
            x_scalar (torch.Tensor): Scalar features
            x_vector (torch.Tensor, optional): Vector features
            
        Returns:
            tuple: Normalized (x_scalar, x_vector)
        """
        # Normalize scalars
        mean = x_scalar.mean(dim=1, keepdim=True)
        var = x_scalar.var(dim=1, keepdim=True, unbiased=False)
        x_scalar = (x_scalar - mean) / (var + 1e-5).sqrt()
        x_scalar = x_scalar * self.scalar_scale.view(1, -1) + self.scalar_bias.view(1, -1)

        # Normalize vectors if provided
        if x_vector is not None and not self.scalar_only:
            vec_norm = torch.norm(x_vector, dim=2, keepdim=True)
            vec_mean = vec_norm.mean(dim=1, keepdim=True)
            vec_var = vec_norm.var(dim=1, keepdim=True, unbiased=False)

            scale = (vec_norm / (vec_var + 1e-5).sqrt()) * self.vector_scale.view(1, -1, 1)
            x_vector = x_vector * scale

        return x_scalar, x_vector
