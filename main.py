"""
Main execution script for Crystal VAE.

This script demonstrates how to use the Crystal VAE for training and generation
of crystal structures with desired properties.
"""

import argparse
import os
import sys
from train import run_crystal_vae
from generate import generate_crystals, batch_generate_crystals, save_generated_structures


def train_model(args):
    """Train a new Crystal VAE model."""
    print("Starting Crystal VAE training...")
    
    model, history = run_crystal_vae(
        dataset_path=args.dataset_path,
        target_name=args.target_name,
        epochs=args.epochs,
        batch_size=args.batch_size,
        hidden_channels=args.hidden_channels,
        latent_dim=args.latent_dim,
        num_conv_layers=args.num_conv_layers,
        kl_weight=args.kl_weight,
        learning_rate=args.learning_rate,
        pretrained_model_path=args.pretrained_model
    )
    
    print("Training completed successfully!")
    return model, history


def generate_structures(args):
    """Generate crystal structures using a trained model."""
    print("Generating crystal structures...")
    
    if args.target_range:
        # Generate for a range of values
        min_val, max_val, num_points = map(float, args.target_range.split(','))
        target_range = (min_val, max_val, int(num_points))
        
        results = batch_generate_crystals(
            model_path=args.model_path,
            target_range=target_range,
            num_samples_per_target=args.num_samples,
            output_dir=args.output_dir,
            save_format=args.save_format
        )
        
        print(f"Generated structures for {len(results['target_values'])} target values")
        
    else:
        # Generate for specific values
        target_values = [float(x) for x in args.target_values.split(',')]
        
        structures = generate_crystals(
            model_path=args.model_path,
            target_values=target_values,
            num_samples=args.num_samples
        )
        
        if args.output_dir:
            save_generated_structures(structures, args.output_dir, args.save_format)
        
        print(f"Generated structures for {len(target_values)} target values")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Crystal VAE - Generate crystal structures with desired properties')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Training command
    train_parser = subparsers.add_parser('train', help='Train a new Crystal VAE model')
    train_parser.add_argument('--dataset-path', required=True, 
                             help='Path to dataset directory containing NPZ and CSV files')
    train_parser.add_argument('--target-name', required=True,
                             help='Name of target property column in CSV file')
    train_parser.add_argument('--epochs', type=int, default=100,
                             help='Number of training epochs (default: 100)')
    train_parser.add_argument('--batch-size', type=int, default=32,
                             help='Batch size for training (default: 32)')
    train_parser.add_argument('--hidden-channels', type=int, default=128,
                             help='Number of hidden channels (default: 128)')
    train_parser.add_argument('--latent-dim', type=int, default=64,
                             help='Latent space dimension (default: 64)')
    train_parser.add_argument('--num-conv-layers', type=int, default=3,
                             help='Number of convolution layers (default: 3)')
    train_parser.add_argument('--kl-weight', type=float, default=0.01,
                             help='Weight for KL divergence loss (default: 0.01)')
    train_parser.add_argument('--learning-rate', type=float, default=1e-4,
                             help='Learning rate (default: 1e-4)')
    train_parser.add_argument('--pretrained-model', type=str,
                             help='Path to pretrained model to continue training')
    
    # Generation command
    generate_parser = subparsers.add_parser('generate', help='Generate crystal structures')
    generate_parser.add_argument('--model-path', required=True,
                                help='Path to trained model checkpoint')
    generate_parser.add_argument('--target-values', type=str,
                                help='Comma-separated target property values (e.g., "1.0,2.0,3.0")')
    generate_parser.add_argument('--target-range', type=str,
                                help='Target range as "min,max,num_points" (e.g., "0.5,3.0,10")')
    generate_parser.add_argument('--num-samples', type=int, default=5,
                                help='Number of samples per target value (default: 5)')
    generate_parser.add_argument('--output-dir', type=str,
                                help='Directory to save generated structures')
    generate_parser.add_argument('--save-format', choices=['npz', 'json', 'xyz'], default='npz',
                                help='Format to save structures (default: npz)')
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.command == 'train':
        if not os.path.exists(args.dataset_path):
            print(f"Error: Dataset path '{args.dataset_path}' does not exist")
            sys.exit(1)
        train_model(args)
        
    elif args.command == 'generate':
        if not os.path.exists(args.model_path):
            print(f"Error: Model path '{args.model_path}' does not exist")
            sys.exit(1)
        
        if not args.target_values and not args.target_range:
            print("Error: Must specify either --target-values or --target-range")
            sys.exit(1)
        
        if args.target_values and args.target_range:
            print("Error: Cannot specify both --target-values and --target-range")
            sys.exit(1)
            
        generate_structures(args)
        
    else:
        parser.print_help()


def example_usage():
    """Print example usage commands."""
    print("Crystal VAE - Example Usage")
    print("=" * 50)
    print()
    print("1. Train a new model:")
    print("   python main.py train \\")
    print("       --dataset-path /path/to/dataset \\")
    print("       --target-name e_form \\")
    print("       --epochs 100 \\")
    print("       --batch-size 32 \\")
    print("       --hidden-channels 128 \\")
    print("       --latent-dim 64")
    print()
    print("2. Generate structures with specific target values:")
    print("   python main.py generate \\")
    print("       --model-path crystal_vae_e_form/best_crystal_vae_e_form.pt \\")
    print("       --target-values \"1.0,2.0,3.0\" \\")
    print("       --num-samples 10 \\")
    print("       --output-dir generated_structures \\")
    print("       --save-format npz")
    print()
    print("3. Generate structures for a range of target values:")
    print("   python main.py generate \\")
    print("       --model-path crystal_vae_e_form/best_crystal_vae_e_form.pt \\")
    print("       --target-range \"0.5,3.0,10\" \\")
    print("       --num-samples 5 \\")
    print("       --output-dir generated_range \\")
    print("       --save-format json")
    print()
    print("4. Continue training from a checkpoint:")
    print("   python main.py train \\")
    print("       --dataset-path /path/to/dataset \\")
    print("       --target-name e_form \\")
    print("       --epochs 50 \\")
    print("       --pretrained-model crystal_vae_e_form/best_crystal_vae_e_form.pt")


if __name__ == "__main__":
    # If no arguments provided, show example usage
    if len(sys.argv) == 1:
        example_usage()
        print()
        print("Run 'python main.py --help' for detailed usage information.")
    else:
        main()


# Example programmatic usage (can be imported and used directly)
def example_programmatic_usage():
    """
    Example of how to use the Crystal VAE programmatically.
    
    This function demonstrates the typical workflow for training and generation.
    """
    # Example dataset path - replace with your actual path
    dataset_path = "/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/MaterialsProjectData/20KSPLIT/"
    
    # 1. Train a model
    print("Training Crystal VAE model...")
    model, history = run_crystal_vae(
        dataset_path=dataset_path,
        target_name="e_form",  # Formation energy
        epochs=1,  # Use more epochs for real training
        batch_size=16,
        hidden_channels=128,
        latent_dim=64,
        num_conv_layers=2,
        kl_weight=0.01,
        learning_rate=1e-4
    )
    
    # 2. Generate structures with the trained model
    print("Generating crystal structures...")
    model_path = "crystal_vae_e_form/best_crystal_vae_e_form.pt"
    target_values = [-1.0, 0.0, 1.0]  # Formation energies
    
    structures = generate_crystals(
        model_path=model_path,
        target_values=target_values,
        num_samples=5
    )
    
    # 3. Save generated structures
    save_generated_structures(
        structures=structures,
        output_dir="generated_crystals",
        format='npz'
    )
    
    print("Example completed successfully!")
    return model, structures


# Configuration for different target properties
PROPERTY_CONFIGS = {
    'e_form': {
        'name': 'Formation Energy',
        'units': 'eV/atom',
        'typical_range': (-3.0, 2.0),
        'description': 'Energy required to form the compound from its constituent elements'
    },
    'bandgap': {
        'name': 'Band Gap',
        'units': 'eV',
        'typical_range': (0.0, 6.0),
        'description': 'Energy difference between valence and conduction bands'
    },
    'e_hull': {
        'name': 'Energy Above Hull',
        'units': 'eV/atom',
        'typical_range': (0.0, 1.0),
        'description': 'Energy above the convex hull of stable phases'
    }
}


def get_property_info(property_name):
    """Get information about a target property."""
    return PROPERTY_CONFIGS.get(property_name, {
        'name': property_name,
        'units': 'unknown',
        'typical_range': (0.0, 1.0),
        'description': 'Custom property'
    })
