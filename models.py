"""
Model architectures for Crystal VAE.

This module contains the main neural network architectures including
the encoder, decoder, and complete VAE model for crystal structure generation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from layers import (
    E3EquivariantCGCNNConv, 
    E3EquivariantPrimaryCapsuleLayer,
    E3EquivariantSecondaryCapsuleLayer,
    E3LayerNorm
)


class EncoderGNN(nn.Module):
    """
    Graph Neural Network encoder for crystal structures.
    
    This encoder processes crystal structure graphs and converts them into
    a latent representation using E(3)-equivariant convolutions and capsule networks.
    """
    
    def __init__(self, node_features, edge_features, hidden_channels, num_conv_layers):
        """
        Initialize the encoder.
        
        Args:
            node_features (int): Number of input node features
            edge_features (int): Number of input edge features
            hidden_channels (int): Number of hidden channels
            num_conv_layers (int): Number of convolution layers
        """
        super().__init__()
        self.hidden_channels = hidden_channels

        # Node embedding
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features + 3, hidden_channels),  # +3 for positions
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU()
        )

        # Edge embedding
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU()
        )

        # Convolution layers
        self.convs = nn.ModuleList([
            E3EquivariantCGCNNConv(hidden_channels)
            for _ in range(num_conv_layers)
        ])

        # Batch normalization layers
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_channels) for _ in range(num_conv_layers)
        ])

        # Layer normalization for E3 equivariance
        self.layer_norms = nn.ModuleList([
            E3LayerNorm(hidden_channels) for _ in range(num_conv_layers)
        ])

        # Primary capsule layer
        self.primary_caps = E3EquivariantPrimaryCapsuleLayer(
            scalar_features=hidden_channels,
            vector_features=hidden_channels,
            out_caps=8,
            caps_dim=16
        )

        # Secondary capsule layer
        self.secondary_caps = E3EquivariantSecondaryCapsuleLayer(
            in_dim=16,
            out_caps=6,
            out_dim=16,
            routing_iterations=3
        )

    def forward(self, data):
        """
        Forward pass through the encoder.
        
        Args:
            data: PyTorch Geometric Data object containing graph information
            
        Returns:
            torch.Tensor: Encoded representation with shape [batch_size, 6, 16]
        """
        x, edge_index, edge_attr, pos = data.x, data.edge_index, data.edge_attr, data.pos
        batch = data.batch if hasattr(data, 'batch') else torch.zeros(x.size(0), dtype=torch.long, device=x.device)

        # Embed nodes (concatenate features with positions)
        x = torch.cat([x, pos], dim=1)
        x = self.node_embedding(x)

        # Embed edges
        edge_attr = self.edge_embedding(edge_attr)

        # Initialize scalar and vector features
        x_scalar = x
        # Initialize vectors from positions
        x_vector = pos.unsqueeze(1).repeat(1, self.hidden_channels, 1)

        # Apply convolution layers
        for i, conv in enumerate(self.convs):
            x_scalar_res, x_vector_res = x_scalar, x_vector

            # Apply convolution
            x_scalar, x_vector = conv(x_scalar, x_vector, edge_index, edge_attr, pos)

            # Apply normalization
            x_scalar, x_vector = self.layer_norms[i](x_scalar, x_vector)
            x_scalar = self.batch_norms[i](x_scalar)
            x_scalar = F.relu(x_scalar)

            # Add residual connections (skip first layer)
            if i > 0:
                x_scalar = x_scalar + x_scalar_res
                x_vector = x_vector + x_vector_res

        # Apply capsule layers
        primary_caps, primary_vectors = self.primary_caps(x_scalar, x_vector)
        secondary_caps, secondary_vectors = self.secondary_caps(primary_caps, primary_vectors, batch)

        return secondary_caps


class CapsuleDecoderLayer(nn.Module):
    """
    Capsule-based decoder layer for generating graph components.

    This layer uses capsule networks to generate node and edge features
    while maintaining E(3)-equivariance through proper geometric operations.
    """

    def __init__(self, in_caps, in_dim, out_caps, out_dim, routing_iterations=3):
        super().__init__()
        self.in_caps = in_caps
        self.in_dim = in_dim
        self.out_caps = out_caps
        self.out_dim = out_dim
        self.routing_iterations = routing_iterations

        # Transformation matrices for capsule routing
        self.W = nn.Parameter(torch.randn(out_caps, in_caps, in_dim, out_dim))
        self.bias = nn.Parameter(torch.zeros(out_caps, out_dim))

    def squash(self, tensor, dim=-1):
        """Squashing function for capsule activations."""
        squared_norm = (tensor ** 2).sum(dim=dim, keepdim=True)
        scale = squared_norm / (1 + squared_norm)
        return scale * tensor / (torch.sqrt(squared_norm) + 1e-8)

    def forward(self, input_capsules):
        """
        Forward pass through capsule decoder layer.

        Args:
            input_capsules: Input capsules [batch_size, in_caps, in_dim]

        Returns:
            Output capsules [batch_size, out_caps, out_dim]
        """
        batch_size = input_capsules.size(0)

        # Compute prediction vectors
        u_hat = torch.zeros(batch_size, self.in_caps, self.out_caps, self.out_dim,
                           device=input_capsules.device)

        for i in range(self.out_caps):
            u_hat[:, :, i, :] = torch.matmul(input_capsules, self.W[i])

        # Dynamic routing
        b_ij = torch.zeros(batch_size, self.in_caps, self.out_caps, device=input_capsules.device)

        for iteration in range(self.routing_iterations):
            c_ij = F.softmax(b_ij, dim=2)
            c_ij = c_ij.unsqueeze(3)

            # Weighted sum
            s_j = (c_ij * u_hat).sum(dim=1) + self.bias.unsqueeze(0)

            # Apply squashing
            v_j = self.squash(s_j, dim=2)

            # Update routing logits
            if iteration < self.routing_iterations - 1:
                agreement = (u_hat * v_j.unsqueeze(1)).sum(dim=3)
                b_ij = b_ij + agreement

        return v_j


class E3EquivariantDecoderLayer(nn.Module):
    """
    E(3)-equivariant decoder layer for generating geometric features.

    This layer generates positions and edge attributes while maintaining
    rotational and translational equivariance.
    """

    def __init__(self, capsule_dim, hidden_channels, num_rbf=16, cutoff=10.0, lmax=1):
        super().__init__()
        self.capsule_dim = capsule_dim
        self.hidden_channels = hidden_channels
        self.num_rbf = num_rbf
        self.cutoff = cutoff
        self.lmax = lmax

        # Import RadialBasisLayer from layers module
        from layers import RadialBasisLayer
        self.rbf = RadialBasisLayer(num_rbf, cutoff)

        # Capsule to scalar/vector projection
        self.caps_to_scalar = nn.Linear(capsule_dim, hidden_channels)
        self.caps_to_vector = nn.Linear(capsule_dim, hidden_channels * 3)

        # Position generation (equivariant)
        self.position_mlp = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels),
            nn.SiLU(),
            nn.Linear(hidden_channels, 3)
        )

        # Edge feature generation with geometric information
        self.edge_scalar_mlp = nn.Sequential(
            nn.Linear(hidden_channels * 2 + num_rbf, hidden_channels),
            nn.SiLU(),
            nn.Linear(hidden_channels, hidden_channels)
        )

        # Spherical harmonics integration
        self.sh_integration = nn.Linear(hidden_channels + (lmax + 1)**2, hidden_channels)

        # Edge existence predictor
        self.edge_existence = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels // 2),
            nn.SiLU(),
            nn.Linear(hidden_channels // 2, 1),
            nn.Sigmoid()
        )

        # Edge attribute generator
        self.edge_attr_generator = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels),
            nn.SiLU(),
            nn.Linear(hidden_channels, 2)  # [distance, type]
        )

    def forward(self, node_capsules):
        """
        Generate positions and edge information from node capsules.

        Args:
            node_capsules: Node capsules [batch_size, num_nodes, capsule_dim]

        Returns:
            positions: Generated positions [batch_size, num_nodes, 3]
            edge_info: Edge existence probabilities and attributes
        """
        batch_size, num_nodes, _ = node_capsules.shape

        # Convert capsules to scalar and vector features
        scalar_features = self.caps_to_scalar(node_capsules)
        vector_raw = self.caps_to_vector(node_capsules)
        vector_features = vector_raw.view(batch_size, num_nodes, -1, 3)

        # Generate positions (equivariant operation)
        positions = self.position_mlp(scalar_features)

        # Generate edge information
        edge_indices = []
        edge_probs = []
        edge_attrs = []

        for b in range(batch_size):
            batch_edges = []
            batch_probs = []
            batch_attrs = []

            for i in range(num_nodes):
                for j in range(i + 1, num_nodes):
                    # Compute geometric features
                    r_ij = positions[b, i] - positions[b, j]
                    d_ij = torch.norm(r_ij, dim=-1, keepdim=True)

                    # RBF encoding of distance
                    rbf_features = self.rbf(d_ij.unsqueeze(0))

                    # Spherical harmonics for angular information
                    if d_ij > 1e-8:
                        from e3nn.o3 import spherical_harmonics
                        sh_features = spherical_harmonics(
                            list(range(self.lmax + 1)),
                            r_ij.unsqueeze(0) / (d_ij + 1e-8),
                            normalize=True
                        )
                    else:
                        sh_features = torch.zeros((1, (self.lmax + 1)**2), device=positions.device)

                    # Combine node features for edge prediction
                    node_pair_features = torch.cat([
                        scalar_features[b, i],
                        scalar_features[b, j],
                        rbf_features.squeeze(0)
                    ], dim=0)

                    edge_hidden = self.edge_scalar_mlp(node_pair_features.unsqueeze(0))

                    # Integrate spherical harmonics
                    combined_features = torch.cat([edge_hidden.squeeze(0), sh_features.squeeze(0)], dim=0)
                    integrated_features = self.sh_integration(combined_features.unsqueeze(0))

                    # Predict edge existence
                    edge_prob = self.edge_existence(integrated_features)

                    # Generate edge attributes if edge exists
                    if edge_prob > 0.5:
                        edge_attr = self.edge_attr_generator(integrated_features)
                        batch_edges.append([i, j])
                        batch_probs.append(edge_prob.item())
                        batch_attrs.append(edge_attr.squeeze(0))

            if batch_edges:
                edge_indices.append(torch.tensor(batch_edges, dtype=torch.long).t())
                edge_probs.append(torch.tensor(batch_probs))
                edge_attrs.append(torch.stack(batch_attrs))
            else:
                # Create dummy edge if no edges generated
                edge_indices.append(torch.tensor([[0], [0]], dtype=torch.long))
                edge_probs.append(torch.tensor([0.0]))
                edge_attrs.append(torch.zeros((1, 2)))

        return positions, edge_indices, edge_probs, edge_attrs


class DecoderGNN(nn.Module):
    """
    Capsule-enhanced E(3)-equivariant decoder for crystal structure generation.

    This decoder uses capsule networks and equivariant operations to generate
    crystal structures while preserving rotational symmetry.
    """

    def __init__(self, latent_dim, hidden_channels, node_features, edge_features, max_nodes=50, device='cuda'):
        """
        Initialize the capsule-enhanced decoder.

        Args:
            latent_dim (int): Dimension of latent space
            hidden_channels (int): Number of hidden channels
            node_features (int): Number of output node features
            edge_features (int): Number of output edge features
            max_nodes (int): Maximum number of nodes to generate
            device (str): Device to use for computation
        """
        super().__init__()
        self.max_nodes = max_nodes
        self.latent_dim = latent_dim
        self.hidden_channels = hidden_channels
        self.node_features = node_features
        self.edge_features = edge_features
        self.device = device

        # Latent to capsule expansion
        self.latent_to_capsules = nn.Sequential(
            nn.Linear(latent_dim + 1, hidden_channels * 2),  # +1 for conditional property
            nn.ReLU(),
            nn.Linear(hidden_channels * 2, 8 * 16),  # 8 capsules, 16 dim each
            nn.ReLU()
        )

        # Primary decoder capsules (latent → node capsules)
        self.primary_decoder_caps = CapsuleDecoderLayer(
            in_caps=8, in_dim=16,
            out_caps=max_nodes, out_dim=32,
            routing_iterations=3
        )

        # Secondary decoder capsules (node capsules → refined node capsules)
        self.secondary_decoder_caps = CapsuleDecoderLayer(
            in_caps=max_nodes, in_dim=32,
            out_caps=max_nodes, out_dim=hidden_channels,
            routing_iterations=3
        )

        # E(3)-equivariant geometric decoder
        self.geometric_decoder = E3EquivariantDecoderLayer(
            capsule_dim=hidden_channels,
            hidden_channels=hidden_channels,
            num_rbf=16,
            cutoff=10.0,
            lmax=1
        )

        # Node feature generator from capsules
        self.node_feature_generator = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, node_features)
        )

    def forward(self, z, target_property, num_nodes=None):
        """
        Forward pass through the capsule-enhanced decoder.

        Args:
            z (torch.Tensor): Latent representation [batch_size, latent_dim]
            target_property (torch.Tensor): Target property values [batch_size, 1]
            num_nodes (int, optional): Number of nodes to generate

        Returns:
            tuple: (node_features_list, pos_list, edge_index_list, edge_attr_list)
        """
        batch_size = z.size(0)

        # Condition latent code with target property
        z_cond = torch.cat([z, target_property.view(-1, 1)], dim=1)

        if num_nodes is None:
            num_nodes = self.max_nodes

        # Step 1: Expand latent to initial capsule representation
        latent_caps_flat = self.latent_to_capsules(z_cond)
        latent_caps = latent_caps_flat.view(batch_size, 8, 16)  # 8 capsules, 16 dim each

        # Step 2: Generate node capsules through capsule layers
        node_caps_primary = self.primary_decoder_caps(latent_caps)  # [batch_size, max_nodes, 32]

        # Trim to actual number of nodes needed
        node_caps_primary = node_caps_primary[:, :num_nodes, :]

        # Step 3: Refine node capsules
        node_caps_refined = self.secondary_decoder_caps(node_caps_primary)  # [batch_size, num_nodes, hidden_channels]

        # Step 4: Generate geometric structure using E(3)-equivariant operations
        positions, edge_indices, edge_probs, edge_attrs = self.geometric_decoder(node_caps_refined)

        # Step 5: Generate final node features from refined capsules
        node_features_batch = self.node_feature_generator(node_caps_refined)

        # Convert batch outputs to list format for compatibility
        node_features_list = []
        pos_list = []
        edge_index_list = []
        edge_attr_list = []

        for b in range(batch_size):
            node_features_list.append(node_features_batch[b])
            pos_list.append(positions[b])
            edge_index_list.append(edge_indices[b].to(self.device))
            edge_attr_list.append(edge_attrs[b].to(self.device))

        return node_features_list, pos_list, edge_index_list, edge_attr_list


class CrystalVAE(nn.Module):
    """
    Variational Autoencoder for crystal structure generation.
    
    This model combines an encoder and decoder to learn a latent representation
    of crystal structures and generate new structures with desired properties.
    """
    
    def __init__(self, node_features, edge_features, hidden_channels, latent_dim=32,
                 num_conv_layers=2, kl_weight=0.01, device='cuda'):
        """
        Initialize the Crystal VAE.
        
        Args:
            node_features (int): Number of node features
            edge_features (int): Number of edge features
            hidden_channels (int): Number of hidden channels
            latent_dim (int): Dimension of latent space
            num_conv_layers (int): Number of convolution layers
            kl_weight (float): Weight for KL divergence loss
            device (str): Device to use for computation
        """
        super().__init__()
        self.latent_dim = latent_dim
        self.kl_weight = kl_weight
        self.device = device

        # Encoder
        self.encoder = EncoderGNN(
            node_features=node_features,
            edge_features=edge_features,
            hidden_channels=hidden_channels,
            num_conv_layers=num_conv_layers
        )

        # Latent space projections
        # Secondary capsules output: [batch_size, 6, 16] -> flatten to [batch_size, 96]
        self.mu_projection = nn.Linear(6 * 16, latent_dim)
        self.logvar_projection = nn.Linear(6 * 16, latent_dim)

        # Decoder
        self.decoder = DecoderGNN(
            latent_dim=latent_dim,
            hidden_channels=hidden_channels,
            node_features=node_features,
            edge_features=edge_features,
            device=self.device
        )

        # Property predictor
        self.property_predictor = nn.Sequential(
            nn.Linear(latent_dim, hidden_channels // 2),
            nn.ReLU(),
            nn.Linear(hidden_channels // 2, 1)
        )

    def encode(self, data):
        """
        Encode graph data to latent space.
        
        Args:
            data: PyTorch Geometric Data object
            
        Returns:
            tuple: (mu, logvar) for latent distribution
        """
        secondary_caps = self.encoder(data)

        # Flatten capsules for projection
        caps_flat = secondary_caps.view(secondary_caps.size(0), -1)

        # Project to latent space
        mu = self.mu_projection(caps_flat)
        logvar = self.logvar_projection(caps_flat)

        return mu, logvar

    def reparameterize(self, mu, logvar):
        """
        Reparameterization trick for VAE.
        
        Args:
            mu (torch.Tensor): Mean of latent distribution
            logvar (torch.Tensor): Log variance of latent distribution
            
        Returns:
            torch.Tensor: Sampled latent representation
        """
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z, target_property, num_nodes=None):
        """
        Decode latent representation to graph structure.
        
        Args:
            z (torch.Tensor): Latent representation
            target_property (torch.Tensor): Target property values
            num_nodes (int, optional): Number of nodes to generate
            
        Returns:
            tuple: Generated graph components
        """
        return self.decoder(z, target_property, num_nodes)

    def forward(self, data, target_property=None):
        """
        Forward pass through the VAE.
        
        Args:
            data: PyTorch Geometric Data object
            target_property (torch.Tensor, optional): Target property values
            
        Returns:
            dict: Dictionary containing all model outputs
        """
        # Encode
        mu, logvar = self.encode(data)

        # Sample from latent space
        z = self.reparameterize(mu, logvar)

        # Predict property if not provided
        if target_property is None:
            pred_property = self.property_predictor(z)
        else:
            pred_property = target_property

        # Decode
        node_features, positions, edge_indices, edge_attrs = self.decode(z, pred_property)

        return {
            'node_features': node_features,
            'positions': positions,
            'edge_indices': edge_indices,
            'edge_attrs': edge_attrs,
            'pred_property': pred_property,
            'mu': mu,
            'logvar': logvar,
            'z': z
        }

    def loss_function(self, data, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar, true_property=None):
        """
        Compute VAE loss with reconstruction and KL divergence terms.

        Args:
            data: Original graph data
            node_features: Generated node features
            positions: Generated positions
            edge_indices: Generated edge indices
            edge_attrs: Generated edge attributes
            pred_property: Predicted property values
            mu: Mean of latent distribution
            logvar: Log variance of latent distribution
            true_property: True property values (optional)

        Returns:
            tuple: (total_loss, loss_components_dict)
        """
        # Reconstruction loss (simplified for now)
        recon_loss = 0.0

        # KL divergence loss
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())

        # Property prediction loss
        property_loss = 0.0
        if pred_property is not None and true_property is not None:
            property_loss = F.mse_loss(pred_property.squeeze(), true_property)
        elif pred_property is not None and hasattr(data, 'y'):
            property_loss = F.mse_loss(pred_property.squeeze(), data.y.squeeze())

        # Total loss
        total_loss = recon_loss + self.kl_weight * kl_loss + property_loss

        return total_loss, {
            'kl_loss': kl_loss,
            'node_recon': 0.0,
            'pos_recon': 0.0,
            'edge_recon': 0.0,
            'prop_loss': property_loss.item() if isinstance(property_loss, torch.Tensor) else property_loss
        }

    def generate(self, num_samples, target_property, num_nodes=None, device=None):
        """
        Generate new crystal structures.

        Args:
            num_samples (int): Number of samples to generate
            target_property (float or torch.Tensor): Target property value(s)
            num_nodes (int, optional): Number of nodes per structure
            device (str, optional): Device to use

        Returns:
            tuple: Generated graph components
        """
        if device is None:
            device = self.device

        # Sample from latent space
        z = torch.randn(num_samples, self.latent_dim, device=device)

        if not isinstance(target_property, torch.Tensor):
            target_property = torch.tensor([target_property] * num_samples, device=device)

        # Decode
        with torch.no_grad():
            node_features, positions, edge_indices, edge_attrs = self.decode(z, target_property, num_nodes)

        return node_features, positions, edge_indices, edge_attrs
