"""
Test the enhanced encoder with sophisticated capsule architecture.
"""

import torch
import torch.nn as nn
from torch_geometric.data import Data, Batch

# Mock the required layers for testing
class MockE3EquivariantCGCNNConv(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        self.linear = nn.Linear(channels, channels)
    
    def forward(self, x_scalar, x_vector, edge_index, edge_attr, pos):
        # Simple mock implementation
        x_scalar_out = self.linear(x_scalar)
        x_vector_out = x_vector + 0.1 * torch.randn_like(x_vector)
        return x_scalar_out, x_vector_out

class MockE3EquivariantPrimaryCapsuleLayer(nn.Module):
    def __init__(self, scalar_features, vector_features, out_caps, caps_dim):
        super().__init__()
        self.out_caps = out_caps
        self.caps_dim = caps_dim
        self.projection = nn.Linear(scalar_features, out_caps * caps_dim)
    
    def forward(self, x_scalar, x_vector):
        batch_size = x_scalar.size(0)
        caps = self.projection(x_scalar).view(batch_size, self.out_caps, self.caps_dim)
        vectors = x_vector[:, :self.out_caps, :]  # Mock vector output
        return caps, vectors

class MockE3LayerNorm(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.norm = nn.LayerNorm(channels)
    
    def forward(self, x_scalar, x_vector):
        return self.norm(x_scalar), x_vector

# Mock the imports
import sys
from unittest.mock import MagicMock

# Create mock modules
mock_layers = MagicMock()
mock_layers.E3EquivariantCGCNNConv = MockE3EquivariantCGCNNConv
mock_layers.E3EquivariantPrimaryCapsuleLayer = MockE3EquivariantPrimaryCapsuleLayer
mock_layers.E3LayerNorm = MockE3LayerNorm

sys.modules['layers'] = mock_layers

# Now import the actual models
from models import EncoderGNN, CapsuleDecoderLayer

def test_enhanced_encoder():
    """Test the enhanced encoder with sophisticated capsule architecture."""
    print("Testing Enhanced Encoder with Sophisticated Capsules...")
    
    # Test parameters
    node_features = 10
    edge_features = 2
    hidden_channels = 64
    num_conv_layers = 2
    batch_size = 2
    
    # Create enhanced encoder
    encoder = EncoderGNN(
        node_features=node_features,
        edge_features=edge_features,
        hidden_channels=hidden_channels,
        num_conv_layers=num_conv_layers
    )
    
    print(f"✅ Enhanced encoder created successfully")
    print(f"   - Primary capsules: 16 caps × 32 dim")
    print(f"   - Primary encoder caps: 16→12 caps, 32→24 dim")
    print(f"   - Secondary encoder caps: 12→6 caps, 24→16 dim")
    
    # Create test data
    num_nodes_per_graph = [8, 6]  # Two graphs with different sizes
    total_nodes = sum(num_nodes_per_graph)
    
    # Node features and positions
    x = torch.randn(total_nodes, node_features)
    pos = torch.randn(total_nodes, 3)
    
    # Create edges (simple chain for each graph)
    edge_indices = []
    edge_attrs = []
    node_offset = 0
    
    for i, num_nodes in enumerate(num_nodes_per_graph):
        # Create chain edges for this graph
        if num_nodes > 1:
            edges = torch.stack([
                torch.arange(num_nodes - 1) + node_offset,
                torch.arange(1, num_nodes) + node_offset
            ])
            edge_indices.append(edges)
            edge_attrs.append(torch.randn(num_nodes - 1, edge_features))
        node_offset += num_nodes
    
    edge_index = torch.cat(edge_indices, dim=1)
    edge_attr = torch.cat(edge_attrs, dim=0)
    
    # Batch assignment
    batch = torch.cat([torch.full((n,), i) for i, n in enumerate(num_nodes_per_graph)])
    
    # Create PyG Data object
    data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, pos=pos, batch=batch)
    
    print(f"✅ Test data created:")
    print(f"   - Total nodes: {total_nodes}")
    print(f"   - Total edges: {edge_index.size(1)}")
    print(f"   - Batch size: {batch_size}")
    
    try:
        # Forward pass through enhanced encoder
        output = encoder(data)
        
        print(f"✅ Enhanced encoder forward pass successful!")
        print(f"   - Input: {total_nodes} nodes across {batch_size} graphs")
        print(f"   - Output shape: {output.shape}")
        print(f"   - Expected shape: ({batch_size}, 6, 16)")
        print(f"   - Output range: [{output.min():.3f}, {output.max():.3f}]")
        
        # Verify output shape
        expected_shape = (batch_size, 6, 16)
        if output.shape == expected_shape:
            print(f"✅ Output shape verification passed!")
            return True
        else:
            print(f"❌ Shape mismatch! Expected {expected_shape}, got {output.shape}")
            return False
            
    except Exception as e:
        print(f"❌ Error in enhanced encoder: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_encoder_decoder_symmetry():
    """Test that encoder and decoder use similar capsule architectures."""
    print("\nTesting Encoder-Decoder Capsule Symmetry...")
    
    # Create encoder
    encoder = EncoderGNN(
        node_features=10,
        edge_features=2,
        hidden_channels=64,
        num_conv_layers=2
    )
    
    # Check encoder capsule layers
    print("📊 Encoder Capsule Architecture:")
    print(f"   - Primary Encoder Caps: {encoder.primary_encoder_caps}")
    print(f"     Input: {encoder.primary_encoder_caps.in_caps} caps × {encoder.primary_encoder_caps.in_dim} dim")
    print(f"     Output: {encoder.primary_encoder_caps.out_caps} caps × {encoder.primary_encoder_caps.out_dim} dim")
    print(f"     Routing iterations: {encoder.primary_encoder_caps.routing_iterations}")
    
    print(f"   - Secondary Encoder Caps: {encoder.secondary_encoder_caps}")
    print(f"     Input: {encoder.secondary_encoder_caps.in_caps} caps × {encoder.secondary_encoder_caps.in_dim} dim")
    print(f"     Output: {encoder.secondary_encoder_caps.out_caps} caps × {encoder.secondary_encoder_caps.out_dim} dim")
    print(f"     Routing iterations: {encoder.secondary_encoder_caps.routing_iterations}")
    
    # Check transformation matrix dimensions
    print("\n🔧 Transformation Matrix Dimensions:")
    print(f"   - Primary Encoder W: {encoder.primary_encoder_caps.W.shape}")
    print(f"   - Secondary Encoder W: {encoder.secondary_encoder_caps.W.shape}")
    
    # Verify both use CapsuleDecoderLayer
    primary_is_sophisticated = isinstance(encoder.primary_encoder_caps, CapsuleDecoderLayer)
    secondary_is_sophisticated = isinstance(encoder.secondary_encoder_caps, CapsuleDecoderLayer)
    
    print(f"\n✅ Sophistication Check:")
    print(f"   - Primary uses CapsuleDecoderLayer: {primary_is_sophisticated}")
    print(f"   - Secondary uses CapsuleDecoderLayer: {secondary_is_sophisticated}")
    
    if primary_is_sophisticated and secondary_is_sophisticated:
        print(f"🎉 SUCCESS: Encoder now uses sophisticated capsule architecture!")
        print(f"   - Same dynamic routing as decoder")
        print(f"   - 4D transformation matrices")
        print(f"   - Efficient batch processing")
        return True
    else:
        print(f"❌ FAILURE: Encoder still uses simplified capsule layers")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("Testing Enhanced Encoder with Sophisticated Capsule Architecture")
    print("=" * 70)
    
    test1_passed = test_enhanced_encoder()
    test2_passed = test_encoder_decoder_symmetry()
    
    print("\n" + "=" * 70)
    if test1_passed and test2_passed:
        print("🎉 All tests passed! Enhanced encoder is working correctly.")
        print("✅ Encoder now uses the same sophisticated capsule architecture as decoder")
        print("✅ True architectural symmetry achieved")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    print("=" * 70)
