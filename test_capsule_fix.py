"""
Quick test to verify the CapsuleDecoderLayer dimension fix.
"""

import torch
from models import CapsuleDecoder<PERSON><PERSON><PERSON>

def test_capsule_decoder_dimensions():
    """Test that the CapsuleDecoderLayer works with correct dimensions."""
    print("Testing CapsuleDecoderLayer dimension fix...")
    
    # Test parameters
    batch_size = 2
    in_caps = 8
    in_dim = 16
    out_caps = 10
    out_dim = 32
    
    # Create layer
    layer = CapsuleDecoderLayer(in_caps, in_dim, out_caps, out_dim, routing_iterations=3)
    
    # Create test input
    input_capsules = torch.randn(batch_size, in_caps, in_dim)
    
    print(f"Input shape: {input_capsules.shape}")
    print(f"Expected output shape: ({batch_size}, {out_caps}, {out_dim})")
    
    try:
        # Forward pass
        output_capsules = layer(input_capsules)
        print(f"✅ Success! Output shape: {output_capsules.shape}")
        print(f"Output range: [{output_capsules.min():.3f}, {output_capsules.max():.3f}]")
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_decoder_integration():
    """Test the full decoder with the fix."""
    print("\nTesting full DecoderGNN integration...")
    
    from models import DecoderGNN
    
    # Parameters
    latent_dim = 64
    hidden_channels = 128
    node_features = 10
    edge_features = 2
    max_nodes = 8
    batch_size = 2
    
    try:
        # Initialize decoder
        decoder = DecoderGNN(
            latent_dim=latent_dim,
            hidden_channels=hidden_channels,
            node_features=node_features,
            edge_features=edge_features,
            max_nodes=max_nodes,
            device='cpu'
        )
        
        # Create test inputs
        z = torch.randn(batch_size, latent_dim)
        target_property = torch.randn(batch_size, 1)
        
        # Forward pass
        node_features_list, pos_list, edge_index_list, edge_attr_list = decoder(z, target_property, num_nodes=6)
        
        print(f"✅ Success! Generated {len(node_features_list)} structures")
        print(f"Node features shape: {node_features_list[0].shape}")
        print(f"Positions shape: {pos_list[0].shape}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Capsule Decoder Dimension Fix")
    print("=" * 50)
    
    test1_passed = test_capsule_decoder_dimensions()
    test2_passed = test_decoder_integration()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 All tests passed! The dimension fix is working.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    print("=" * 50)
