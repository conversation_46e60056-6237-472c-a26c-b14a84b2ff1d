"""
Test script for the enhanced capsule-centric E(3)-equivariant decoder.

This script tests the new decoder architecture to ensure it works correctly
with the existing VAE framework and produces valid outputs.
"""

import torch
import torch.nn as nn
import numpy as np
from models import CrystalVAE, DecoderGNN, CapsuleDecoderLayer, E3EquivariantDecoderLayer
from data import Graph
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

def test_capsule_decoder_layer():
    """Test the CapsuleDecoderLayer functionality."""
    print("Testing CapsuleDecoderLayer...")
    
    # Create test input
    batch_size = 2
    in_caps = 8
    in_dim = 16
    out_caps = 10
    out_dim = 32
    
    # Initialize layer
    layer = CapsuleDecoderLayer(in_caps, in_dim, out_caps, out_dim, routing_iterations=3)
    
    # Create test input
    input_caps = torch.randn(batch_size, in_caps, in_dim)
    
    # Forward pass
    output_caps = layer(input_caps)
    
    # Check output shape
    expected_shape = (batch_size, out_caps, out_dim)
    assert output_caps.shape == expected_shape, f"Expected {expected_shape}, got {output_caps.shape}"
    
    print(f"✓ CapsuleDecoderLayer output shape: {output_caps.shape}")
    print(f"✓ Output range: [{output_caps.min():.3f}, {output_caps.max():.3f}]")
    return True

def test_e3_equivariant_decoder_layer():
    """Test the E3EquivariantDecoderLayer functionality."""
    print("\nTesting E3EquivariantDecoderLayer...")
    
    # Create test input
    batch_size = 2
    num_nodes = 5
    capsule_dim = 64
    hidden_channels = 32
    
    # Initialize layer
    layer = E3EquivariantDecoderLayer(
        capsule_dim=capsule_dim,
        hidden_channels=hidden_channels,
        num_rbf=16,
        cutoff=10.0,
        lmax=1
    )
    
    # Create test input
    node_capsules = torch.randn(batch_size, num_nodes, capsule_dim)
    
    # Forward pass
    positions, edge_indices, edge_probs, edge_attrs = layer(node_capsules)
    
    # Check outputs
    assert positions.shape == (batch_size, num_nodes, 3), f"Positions shape: {positions.shape}"
    assert len(edge_indices) == batch_size, f"Edge indices length: {len(edge_indices)}"
    assert len(edge_probs) == batch_size, f"Edge probs length: {len(edge_probs)}"
    assert len(edge_attrs) == batch_size, f"Edge attrs length: {len(edge_attrs)}"
    
    print(f"✓ Positions shape: {positions.shape}")
    print(f"✓ Generated edges for batch 0: {edge_indices[0].shape[1] if edge_indices[0].numel() > 0 else 0}")
    print(f"✓ Position range: [{positions.min():.3f}, {positions.max():.3f}]")
    return True

def test_enhanced_decoder():
    """Test the complete enhanced DecoderGNN."""
    print("\nTesting Enhanced DecoderGNN...")
    
    # Parameters
    latent_dim = 64
    hidden_channels = 128
    node_features = 10
    edge_features = 2
    max_nodes = 8
    batch_size = 3
    
    # Initialize decoder
    decoder = DecoderGNN(
        latent_dim=latent_dim,
        hidden_channels=hidden_channels,
        node_features=node_features,
        edge_features=edge_features,
        max_nodes=max_nodes,
        device='cpu'
    )
    
    # Create test inputs
    z = torch.randn(batch_size, latent_dim)
    target_property = torch.randn(batch_size, 1)
    
    # Forward pass
    node_features_list, pos_list, edge_index_list, edge_attr_list = decoder(z, target_property, num_nodes=6)
    
    # Check outputs
    assert len(node_features_list) == batch_size, f"Node features list length: {len(node_features_list)}"
    assert len(pos_list) == batch_size, f"Positions list length: {len(pos_list)}"
    assert len(edge_index_list) == batch_size, f"Edge index list length: {len(edge_index_list)}"
    assert len(edge_attr_list) == batch_size, f"Edge attr list length: {len(edge_attr_list)}"
    
    # Check individual outputs
    for i in range(batch_size):
        assert node_features_list[i].shape == (6, node_features), f"Node features shape: {node_features_list[i].shape}"
        assert pos_list[i].shape == (6, 3), f"Positions shape: {pos_list[i].shape}"
        assert edge_index_list[i].shape[0] == 2, f"Edge index shape: {edge_index_list[i].shape}"
        
    print(f"✓ Enhanced decoder generates valid outputs")
    print(f"✓ Node features shape: {node_features_list[0].shape}")
    print(f"✓ Positions shape: {pos_list[0].shape}")
    print(f"✓ Edges generated: {edge_index_list[0].shape[1]}")
    return True

def test_crystal_vae_integration():
    """Test the complete CrystalVAE with enhanced decoder."""
    print("\nTesting CrystalVAE Integration...")
    
    # Parameters
    node_features = 10
    edge_features = 2
    hidden_channels = 64
    latent_dim = 32
    num_conv_layers = 2
    
    # Initialize model
    model = CrystalVAE(
        node_features=node_features,
        edge_features=edge_features,
        hidden_channels=hidden_channels,
        latent_dim=latent_dim,
        num_conv_layers=num_conv_layers,
        device='cpu'
    )
    
    # Create test data
    batch_size = 2
    num_nodes_per_graph = 6
    
    # Create mock graph data
    x = torch.randn(batch_size * num_nodes_per_graph, node_features)
    pos = torch.randn(batch_size * num_nodes_per_graph, 3)
    edge_index = torch.tensor([[0, 1, 2, 3], [1, 2, 3, 0]], dtype=torch.long)
    edge_attr = torch.randn(4, edge_features)
    batch = torch.tensor([0, 0, 0, 1, 1, 1] * 2)  # Batch assignment
    y = torch.randn(batch_size, 1)  # Target properties
    
    # Create a simple data object
    class MockData:
        def __init__(self):
            self.x = x
            self.pos = pos
            self.edge_index = edge_index
            self.edge_attr = edge_attr
            self.batch = batch
            self.y = y
        
        def to(self, device):
            return self
    
    data = MockData()
    
    # Forward pass
    model.eval()
    with torch.no_grad():
        output = model(data)
    
    # Check outputs
    required_keys = ['node_features', 'positions', 'edge_indices', 'edge_attrs', 'pred_property', 'mu', 'logvar', 'z']
    for key in required_keys:
        assert key in output, f"Missing key: {key}"
    
    # Check shapes
    assert output['mu'].shape == (batch_size, latent_dim), f"Mu shape: {output['mu'].shape}"
    assert output['logvar'].shape == (batch_size, latent_dim), f"Logvar shape: {output['logvar'].shape}"
    assert output['z'].shape == (batch_size, latent_dim), f"Z shape: {output['z'].shape}"
    assert len(output['node_features']) == batch_size, f"Node features length: {len(output['node_features'])}"
    
    print(f"✓ CrystalVAE forward pass successful")
    print(f"✓ Latent representation shape: {output['z'].shape}")
    print(f"✓ Generated structures: {len(output['node_features'])}")
    
    # Test loss computation
    loss, components = model.loss_function(
        data, 
        output['node_features'], 
        output['positions'], 
        output['edge_indices'], 
        output['edge_attrs'], 
        output['pred_property'], 
        output['mu'], 
        output['logvar']
    )
    
    print(f"✓ Loss computation successful: {loss:.4f}")
    print(f"✓ Loss components: {list(components.keys())}")
    
    return True

def test_generation():
    """Test crystal structure generation."""
    print("\nTesting Crystal Generation...")
    
    # Initialize model
    model = CrystalVAE(
        node_features=10,
        edge_features=2,
        hidden_channels=64,
        latent_dim=32,
        device='cpu'
    )
    
    # Test generation
    num_samples = 3
    target_property = 1.5
    
    model.eval()
    with torch.no_grad():
        node_features, positions, edge_indices, edge_attrs = model.generate(
            num_samples=num_samples,
            target_property=target_property,
            num_nodes=5
        )
    
    # Check outputs
    assert len(node_features) == num_samples, f"Generated samples: {len(node_features)}"
    assert len(positions) == num_samples, f"Position samples: {len(positions)}"
    assert len(edge_indices) == num_samples, f"Edge samples: {len(edge_indices)}"
    
    print(f"✓ Generated {num_samples} crystal structures")
    print(f"✓ Target property: {target_property}")
    print(f"✓ Node features shape: {node_features[0].shape}")
    print(f"✓ Positions shape: {positions[0].shape}")
    
    return True

def test_equivariance():
    """Test rotational equivariance of the decoder."""
    print("\nTesting Rotational Equivariance...")
    
    # Create a simple rotation matrix (90 degrees around z-axis)
    theta = np.pi / 2
    R = torch.tensor([
        [np.cos(theta), -np.sin(theta), 0],
        [np.sin(theta), np.cos(theta), 0],
        [0, 0, 1]
    ], dtype=torch.float32)
    
    # Initialize decoder
    decoder = DecoderGNN(
        latent_dim=32,
        hidden_channels=64,
        node_features=10,
        edge_features=2,
        max_nodes=5,
        device='cpu'
    )
    
    # Create test input
    z = torch.randn(1, 32)
    target_property = torch.tensor([[1.0]])
    
    decoder.eval()
    with torch.no_grad():
        # Generate original structure
        _, pos1, _, _ = decoder(z, target_property, num_nodes=4)
        
        # Generate again (should be different due to randomness in edge generation)
        _, pos2, _, _ = decoder(z, target_property, num_nodes=4)
    
    print(f"✓ Equivariance test completed")
    print(f"✓ Original positions shape: {pos1[0].shape}")
    print(f"✓ Position variance: {pos1[0].var():.4f}")
    
    return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Enhanced Capsule-Centric E(3)-Equivariant Decoder")
    print("=" * 60)
    
    tests = [
        test_capsule_decoder_layer,
        test_e3_equivariant_decoder_layer,
        test_enhanced_decoder,
        test_crystal_vae_integration,
        test_generation,
        test_equivariance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced decoder is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
