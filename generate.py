"""
Crystal generation utilities for Crystal VAE.

This module contains functions for generating new crystal structures
using trained Crystal VAE models with specific target properties.
"""

import torch
from models import CrystalVAE


def generate_crystals(model_path, target_values, num_samples=5, device=None):
    """
    Generate crystal structures with specific target properties.
    
    This function loads a trained Crystal VAE model and generates new crystal
    structures with desired property values.
    
    Args:
        model_path (str): Path to the saved model checkpoint
        target_values (list): List of target property values to generate structures for
        num_samples (int): Number of samples to generate for each target value
        device (str, optional): Device to use for generation ('cuda' or 'cpu')
        
    Returns:
        dict: Dictionary mapping target values to generated structures
              Each entry contains (node_features, positions, edge_indices, edge_attrs)
              
    Example:
        >>> target_values = [1.0, 2.0, 3.0]  # Target bandgap values
        >>> structures = generate_crystals('model.pt', target_values, num_samples=10)
        >>> print(f"Generated {len(structures[1.0])} structures with bandgap ~1.0 eV")
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    print(f"Loading model from {model_path}")
    print(f"Using device: {device}")

    # Load model checkpoint
    try:
        checkpoint = torch.load(model_path, map_location=device)
        model_config = checkpoint['model_config']
        
        print("Model configuration:")
        for key, value in model_config.items():
            print(f"  {key}: {value}")

        # Initialize model with saved configuration
        model = CrystalVAE(
            node_features=model_config['node_features'],
            edge_features=model_config['edge_features'],
            hidden_channels=model_config['hidden_channels'],
            latent_dim=model_config['latent_dim'],
            device=device
        ).to(device)

        # Load trained weights
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print("✓ Model loaded successfully")
        
    except Exception as e:
        raise RuntimeError(f"Failed to load model: {str(e)}")

    generated_structures = {}

    print(f"\nGenerating structures for {len(target_values)} target values...")
    
    for target in target_values:
        print(f"Generating {num_samples} structures with target property {target}...")
        
        try:
            # Generate structures using the model
            node_features, positions, edge_indices, edge_attrs = model.generate(
                num_samples=num_samples,
                target_property=target,
                device=device
            )
            
            generated_structures[target] = {
                'node_features': node_features,
                'positions': positions,
                'edge_indices': edge_indices,
                'edge_attrs': edge_attrs
            }
            
            print(f"✓ Generated {len(node_features)} structures with target property {target}")
            
        except Exception as e:
            print(f"✗ Failed to generate structures for target {target}: {str(e)}")
            continue

    print(f"\n✓ Generation complete. Generated structures for {len(generated_structures)} target values.")
    return generated_structures


def save_generated_structures(structures, output_dir, format='npz'):
    """
    Save generated crystal structures to files.
    
    Args:
        structures (dict): Generated structures from generate_crystals()
        output_dir (str): Directory to save structures
        format (str): Output format ('npz', 'json', or 'xyz')
        
    Example:
        >>> structures = generate_crystals('model.pt', [1.0, 2.0])
        >>> save_generated_structures(structures, 'generated_crystals/')
    """
    import os
    import numpy as np
    import json
    
    os.makedirs(output_dir, exist_ok=True)
    
    for target_value, structure_data in structures.items():
        target_dir = os.path.join(output_dir, f"target_{target_value}")
        os.makedirs(target_dir, exist_ok=True)
        
        node_features = structure_data['node_features']
        positions = structure_data['positions']
        edge_indices = structure_data['edge_indices']
        edge_attrs = structure_data['edge_attrs']
        
        for i in range(len(node_features)):
            if format == 'npz':
                # Save as NumPy arrays
                filename = os.path.join(target_dir, f"structure_{i}.npz")
                np.savez(
                    filename,
                    node_features=node_features[i].cpu().numpy(),
                    positions=positions[i].cpu().numpy(),
                    edge_index=edge_indices[i].cpu().numpy(),
                    edge_attr=edge_attrs[i].cpu().numpy(),
                    target_property=target_value
                )
                
            elif format == 'json':
                # Save as JSON (convert tensors to lists)
                filename = os.path.join(target_dir, f"structure_{i}.json")
                structure_dict = {
                    'node_features': node_features[i].cpu().numpy().tolist(),
                    'positions': positions[i].cpu().numpy().tolist(),
                    'edge_index': edge_indices[i].cpu().numpy().tolist(),
                    'edge_attr': edge_attrs[i].cpu().numpy().tolist(),
                    'target_property': float(target_value)
                }
                with open(filename, 'w') as f:
                    json.dump(structure_dict, f, indent=2)
                    
            elif format == 'xyz':
                # Save as XYZ format (positions only)
                filename = os.path.join(target_dir, f"structure_{i}.xyz")
                pos = positions[i].cpu().numpy()
                with open(filename, 'w') as f:
                    f.write(f"{len(pos)}\n")
                    f.write(f"Generated crystal structure, target property: {target_value}\n")
                    for j, coord in enumerate(pos):
                        f.write(f"C {coord[0]:.6f} {coord[1]:.6f} {coord[2]:.6f}\n")
    
    print(f"✓ Saved generated structures to {output_dir} in {format} format")


def analyze_generated_structures(structures):
    """
    Analyze properties of generated crystal structures.
    
    Args:
        structures (dict): Generated structures from generate_crystals()
        
    Returns:
        dict: Analysis results including statistics and properties
    """
    import numpy as np
    
    analysis = {}
    
    for target_value, structure_data in structures.items():
        node_features = structure_data['node_features']
        positions = structure_data['positions']
        edge_indices = structure_data['edge_indices']
        edge_attrs = structure_data['edge_attrs']
        
        # Calculate statistics
        num_structures = len(node_features)
        num_nodes = [nf.shape[0] for nf in node_features]
        num_edges = [ei.shape[1] for ei in edge_indices]
        
        # Position statistics
        all_positions = torch.cat(positions, dim=0).cpu().numpy()
        position_stats = {
            'mean': np.mean(all_positions, axis=0).tolist(),
            'std': np.std(all_positions, axis=0).tolist(),
            'min': np.min(all_positions, axis=0).tolist(),
            'max': np.max(all_positions, axis=0).tolist()
        }
        
        # Edge statistics
        if edge_attrs:
            all_edge_attrs = torch.cat(edge_attrs, dim=0).cpu().numpy()
            edge_stats = {
                'mean': np.mean(all_edge_attrs, axis=0).tolist(),
                'std': np.std(all_edge_attrs, axis=0).tolist(),
                'min': np.min(all_edge_attrs, axis=0).tolist(),
                'max': np.max(all_edge_attrs, axis=0).tolist()
            }
        else:
            edge_stats = None
        
        analysis[target_value] = {
            'num_structures': num_structures,
            'num_nodes': {
                'mean': np.mean(num_nodes),
                'std': np.std(num_nodes),
                'min': np.min(num_nodes),
                'max': np.max(num_nodes)
            },
            'num_edges': {
                'mean': np.mean(num_edges),
                'std': np.std(num_edges),
                'min': np.min(num_edges),
                'max': np.max(num_edges)
            },
            'position_stats': position_stats,
            'edge_stats': edge_stats
        }
    
    return analysis


def visualize_generated_structure(node_features, positions, edge_indices, title="Generated Crystal Structure"):
    """
    Visualize a single generated crystal structure.
    
    Args:
        node_features (torch.Tensor): Node features
        positions (torch.Tensor): Node positions
        edge_indices (torch.Tensor): Edge connectivity
        title (str): Plot title
    """
    try:
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D
        
        # Convert to numpy if needed
        if hasattr(positions, 'cpu'):
            positions = positions.cpu().numpy()
        if hasattr(edge_indices, 'cpu'):
            edge_indices = edge_indices.cpu().numpy()
        
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # Plot nodes
        ax.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                  c='red', s=100, alpha=0.8, label='Atoms')
        
        # Plot edges
        for i in range(edge_indices.shape[1]):
            start_idx, end_idx = edge_indices[:, i]
            start_pos = positions[start_idx]
            end_pos = positions[end_idx]
            ax.plot([start_pos[0], end_pos[0]], 
                   [start_pos[1], end_pos[1]], 
                   [start_pos[2], end_pos[2]], 
                   'b-', alpha=0.6, linewidth=1)
        
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(title)
        ax.legend()
        
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("Matplotlib not available for visualization")
    except Exception as e:
        print(f"Visualization failed: {str(e)}")


def batch_generate_crystals(model_path, target_range, num_samples_per_target=5, 
                          output_dir=None, save_format='npz'):
    """
    Generate crystals for a range of target values in batch.
    
    Args:
        model_path (str): Path to trained model
        target_range (tuple): (min_value, max_value, num_points) for target range
        num_samples_per_target (int): Number of samples per target value
        output_dir (str, optional): Directory to save results
        save_format (str): Format to save structures ('npz', 'json', 'xyz')
        
    Returns:
        dict: Generated structures and analysis
    """
    import numpy as np
    
    min_val, max_val, num_points = target_range
    target_values = np.linspace(min_val, max_val, num_points).tolist()
    
    print(f"Generating crystals for {num_points} target values from {min_val} to {max_val}")
    print(f"Samples per target: {num_samples_per_target}")
    
    # Generate structures
    structures = generate_crystals(model_path, target_values, num_samples_per_target)
    
    # Analyze structures
    analysis = analyze_generated_structures(structures)
    
    # Save if output directory provided
    if output_dir:
        save_generated_structures(structures, output_dir, save_format)
        
        # Save analysis
        import json
        import os
        with open(os.path.join(output_dir, 'analysis.json'), 'w') as f:
            json.dump(analysis, f, indent=2)
        print(f"✓ Saved analysis to {output_dir}/analysis.json")
    
    return {
        'structures': structures,
        'analysis': analysis,
        'target_values': target_values
    }
