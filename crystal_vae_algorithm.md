# Crystal VAE Algorithm Pseudocode

## Algorithm 1: E(3)-Equivariant Crystal VAE Training

```
Input: Dataset D = {(G_i, y_i)}_{i=1}^N where G_i = (V_i, E_i, X_i, P_i) and y_i ∈ ℝ
       G_i: crystal graph with nodes V_i, edges E_i, features X_i, positions P_i
       y_i: target property (e.g., formation energy, band gap)
       
Parameters: θ_enc (encoder), θ_dec (decoder), θ_prop (property predictor)
           λ_KL (KL weight), λ_prop (property weight)

Output: Trained model parameters θ* = {θ_enc*, θ_dec*, θ_prop*}

1: Initialize θ_enc, θ_dec, θ_prop randomly
2: for epoch = 1 to max_epochs do
3:    for batch B ⊆ D do
4:       // Forward pass through encoder
5:       for (G, y) ∈ B do
6:          h_scalar, h_vector ← E3_ENCODER(G; θ_enc)
7:          μ, log σ² ← LINEAR_PROJECTION(h_scalar)
8:          z ← REPARAMETERIZE(μ, log σ²)
9:       end for
10:      
11:      // Property prediction
12:      ŷ ← PROPERTY_PREDICTOR(z; θ_prop)
13:      
14:      // Decoder reconstruction
15:      X̂, P̂, Ê ← E3_DECODER(z, ŷ; θ_dec)
16:      
17:      // Compute losses
18:      L_KL ← -½ Σ(1 + log σ² - μ² - σ²)
19:      L_prop ← MSE(ŷ, y)
20:      L_recon ← RECONSTRUCTION_LOSS(X̂, P̂, Ê, X, P, E)
21:      L_total ← L_recon + λ_KL × L_KL + λ_prop × L_prop
22:      
23:      // Backward pass
24:      ∇θ ← BACKPROP(L_total)
25:      θ ← OPTIMIZER_UPDATE(θ, ∇θ)
26:   end for
27: end for
28: return θ*
```

## Algorithm 2: E(3)-Equivariant Graph Encoder

```
Input: Crystal graph G = (V, E, X, P) where:
       V: nodes (atoms), E: edges (bonds)
       X ∈ ℝ^{|V|×d_node}: node features
       P ∈ ℝ^{|V|×3}: atomic positions

Output: Latent representation z ∈ ℝ^d_latent

1: // Initial embeddings
2: X_scalar ← NODE_EMBEDDING(concat(X, P))
3: X_vector ← EXPAND_POSITIONS(P, d_hidden)
4: E_attr ← EDGE_EMBEDDING(E)

5: // E(3)-equivariant convolutions
6: for layer = 1 to L_conv do
7:    X_scalar_res, X_vector_res ← X_scalar, X_vector
8:    
9:    // Compute edge vectors and distances
10:   for (i,j) ∈ E do
11:      r_ij ← P_i - P_j
12:      d_ij ← ||r_ij||
13:      ψ_ij ← RBF_EXPANSION(d_ij)
14:   end for
15:   
16:   // Scalar message passing
17:   for (i,j) ∈ E do
18:      m_scalar_ij ← MLP_scalar(concat(X_scalar_i, X_scalar_j, ψ_ij))
19:   end for
20:   X_scalar ← AGGREGATE(m_scalar_ij) + X_scalar
21:   
22:   // Vector message passing (E(3)-equivariant)
23:   for (i,j) ∈ E do
24:      Y_ij ← SPHERICAL_HARMONICS(r_ij/d_ij, l_max)
25:      m_vector_ij ← TENSOR_PRODUCT(X_vector_i, Y_ij)
26:   end for
27:   X_vector ← AGGREGATE(m_vector_ij) + X_vector
28:   
29:   // Apply normalization and residual connections
30:   X_scalar, X_vector ← E3_LAYER_NORM(X_scalar, X_vector)
31:   if layer > 1 then
32:      X_scalar ← X_scalar + X_scalar_res
33:      X_vector ← X_vector + X_vector_res
34:   end if
35: end for

36: // Sophisticated capsule network encoding
37: C_primary ← E3_PRIMARY_CAPSULES(X_scalar, X_vector)  // [num_nodes, 16, 32]
38: C_batched ← GLOBAL_MEAN_POOL(C_primary, batch)       // [batch_size, 16, 32]
39:
40: // Hierarchical sophisticated capsule transformations
41: C_refined ← SOPHISTICATED_CAPSULE_LAYER_1(C_batched)  // [batch_size, 12, 24]
42:    // Uses 4D transformation matrix W[12, 16, 32, 24]
43:    // Dynamic routing with prediction vectors
44:
45: C_final ← SOPHISTICATED_CAPSULE_LAYER_2(C_refined)    // [batch_size, 6, 16]
46:    // Uses 4D transformation matrix W[6, 12, 24, 16]
47:    // Dynamic routing with prediction vectors
48:
49: h ← FLATTEN(C_final)  // [batch_size, 96]
50:
51: // Project to latent space
52: μ ← LINEAR(h)
53: log σ² ← LINEAR(h)
54: z ← μ + σ ⊙ ε, where ε ~ N(0, I)

44: return z
```

## Algorithm 3: Sophisticated Capsule Layer with Dynamic Routing

```
Input: Input capsules C_in ∈ ℝ^{B×N_in×d_in}
       B: batch size, N_in: input capsules, d_in: input dimension

Output: Output capsules C_out ∈ ℝ^{B×N_out×d_out}
        N_out: output capsules, d_out: output dimension

1: // Initialize 4D transformation matrices (sophisticated architecture)
2: W ∈ ℝ^{N_out×N_in×d_in×d_out} ← RANDOM_INIT()
3: b ∈ ℝ^{N_out×d_out} ← ZEROS()

4: // Compute prediction vectors using efficient tensor operations
5: û ← EINSUM('bid,oide->bioe', C_in, W)  // [B, N_in, N_out, d_out]
6:    // b=batch, i=input_caps, d=input_dim, o=output_caps, e=output_dim

7: // Dynamic routing with sophisticated agreement mechanism
8: b ← ZEROS(B, N_in, N_out)  // Routing logits [batch, input_caps, output_caps]
9: for iter = 1 to R_iterations do
10:    c ← SOFTMAX(b, dim=2)  // Coupling coefficients [B, N_in, N_out]
11:
12:    // Weighted sum for each output capsule (efficient batch processing)
13:    s ← SUM(c.unsqueeze(-1) × û, dim=1)  // [B, N_out, d_out]
14:    v ← SQUASH(s, dim=-1)  // Non-linear activation [B, N_out, d_out]
15:
16:    // Update routing logits based on agreement (except last iteration)
17:    if iter < R_iterations then
18:       agreement ← SUM(û × v.unsqueeze(1), dim=-1)  // [B, N_in, N_out]
19:       b ← b + agreement  // Update routing coefficients
20:    end if
21: end for
22:
23: // Add bias and return final capsules
24: C_out ← v + b.unsqueeze(0)  // [B, N_out, d_out]
25: return C_out
26:          end for
27:       end for
28:    end if
29: end for

30: return V = [v_1, v_2, ..., v_M]

Function SQUASH(s):
    ||s||² / (1 + ||s||²) × s / ||s||
```

## Algorithm 4: Capsule-Enhanced E(3)-Equivariant Crystal Generation

```
Input: Target property y_target ∈ ℝ
       Trained capsule decoder θ_dec
       Number of samples N_samples

Output: Generated crystal structures {Ĝ_i}_{i=1}^{N_samples}

1: for i = 1 to N_samples do
2:    // Sample from latent space
3:    z_i ~ N(0, I) ∈ ℝ^{d_latent}
4:
5:    // Condition on target property
6:    z_cond_i ← concat(z_i, y_target)
7:
8:    // Step 1: Expand latent to initial capsule space
9:    C_latent_i ← LATENT_TO_CAPSULES(z_cond_i)  // [K_init × d_caps]
10:
11:   // Step 2: Generate node capsules via dynamic routing
12:   C_primary_i ← PRIMARY_DECODER_CAPSULES(C_latent_i)  // [N_nodes × d_primary]
13:   C_nodes_i ← SECONDARY_DECODER_CAPSULES(C_primary_i)  // [N_nodes × d_node]
14:
15:   // Step 3: Generate positions (E(3)-equivariant)
16:   X_scalar_i ← CAPSULES_TO_SCALAR(C_nodes_i)
17:   X_vector_i ← CAPSULES_TO_VECTOR(C_nodes_i)
18:   P̂_i ← POSITION_MLP(X_scalar_i)  // Equivariant position generation
19:
20:   // Step 4: Generate edges with geometric awareness
21:   Ê_i ← ∅
22:   for u = 1 to N_nodes do
23:      for v = u+1 to N_nodes do
24:         // Compute geometric features (E(3)-invariant)
25:         r_uv ← P̂_i[u] - P̂_i[v]
26:         d_uv ← ||r_uv||
27:         ψ_uv ← RBF_EXPANSION(d_uv)  // Radial basis functions
28:         Y_uv ← SPHERICAL_HARMONICS(r_uv/d_uv)  // Angular features
29:
30:         // Capsule-based edge prediction
31:         h_edge ← EDGE_SCALAR_MLP(concat(X_scalar_i[u], X_scalar_i[v], ψ_uv))
32:         h_integrated ← SH_INTEGRATION(concat(h_edge, Y_uv))
33:
34:         // Predict edge existence and attributes
35:         p_edge ← EDGE_EXISTENCE_PREDICTOR(h_integrated)
36:         if p_edge > threshold then
37:            e_attr ← EDGE_ATTR_GENERATOR(h_integrated)
38:            Ê_i ← Ê_i ∪ {(u, v, e_attr)}
39:         end if
40:      end for
41:   end for
42:
43:   // Step 5: Generate final node features from capsules
44:   X̂_i ← NODE_FEATURE_GENERATOR(C_nodes_i)
45:
46:   // Construct generated graph
47:   Ĝ_i ← (V̂_i, Ê_i, X̂_i, P̂_i)
48: end for

49: return {Ĝ_i}_{i=1}^{N_samples}
```

## Algorithm 5: Capsule Decoder Layer with Dynamic Routing

```
Input: Input capsules C_in ∈ ℝ^{batch×N_in×d_in}
       Transformation matrices W ∈ ℝ^{N_out×N_in×d_in×d_out}
       Bias b ∈ ℝ^{N_out×d_out}

Output: Output capsules C_out ∈ ℝ^{batch×N_out×d_out}

1: // Compute prediction vectors for all capsule pairs
2: for batch_idx = 1 to batch_size do
3:    for i = 1 to N_in do
4:       for j = 1 to N_out do
5:          û_ij ← W_j × C_in[batch_idx, i]  // Linear transformation
6:       end for
7:    end for
8: end for

9: // Initialize routing logits
10: b_ij ← ZEROS(batch_size, N_in, N_out)

11: // Dynamic routing iterations
12: for iter = 1 to R_iterations do
13:    // Compute coupling coefficients
14:    c_ij ← SOFTMAX(b_ij, dim=2)  // Normalize over output capsules
15:
16:    // Weighted sum for each output capsule
17:    for batch_idx = 1 to batch_size do
18:       for j = 1 to N_out do
19:          s_j ← Σ_i c_ij[batch_idx, i, j] × û_ij[batch_idx, i, j] + b_j
20:          v_j ← SQUASH(s_j)  // Apply squashing activation
21:          C_out[batch_idx, j] ← v_j
22:       end for
23:    end for
24:
25:    // Update routing logits (except last iteration)
26:    if iter < R_iterations then
27:       for batch_idx = 1 to batch_size do
28:          for i = 1 to N_in do
29:             for j = 1 to N_out do
30:                agreement ← û_ij[batch_idx, i, j] · C_out[batch_idx, j]
31:                b_ij[batch_idx, i, j] ← b_ij[batch_idx, i, j] + agreement
32:             end for
33:          end for
34:       end for
35:    end if
36: end for

37: return C_out

Function SQUASH(s):
    ||s||² / (1 + ||s||²) × s / (||s|| + ε)
```

## Algorithm 6: E(3)-Equivariant Geometric Decoder

```
Input: Node capsules C_nodes ∈ ℝ^{batch×N_nodes×d_caps}
       RBF parameters (centers, widths)
       Spherical harmonics degree l_max

Output: Positions P̂ ∈ ℝ^{batch×N_nodes×3}
        Edge indices Ê, Edge attributes Ê_attr

1: // Convert capsules to scalar and vector features
2: for batch_idx = 1 to batch_size do
3:    X_scalar[batch_idx] ← CAPS_TO_SCALAR(C_nodes[batch_idx])
4:    X_vector_raw[batch_idx] ← CAPS_TO_VECTOR(C_nodes[batch_idx])
5:    X_vector[batch_idx] ← RESHAPE(X_vector_raw[batch_idx], [N_nodes, d_hidden, 3])
6: end for

7: // Generate positions (equivariant operation)
8: P̂ ← POSITION_MLP(X_scalar)  // [batch, N_nodes, 3]

9: // Generate edges with geometric awareness
10: for batch_idx = 1 to batch_size do
11:    Ê[batch_idx] ← ∅
12:    Ê_attr[batch_idx] ← ∅
13:
14:    for u = 1 to N_nodes do
15:       for v = u+1 to N_nodes do
16:          // Compute geometric invariants and covariants
17:          r_uv ← P̂[batch_idx, u] - P̂[batch_idx, v]
18:          d_uv ← ||r_uv||  // Distance (invariant)
19:
20:          // Radial basis function expansion
21:          ψ_uv ← RBF_EXPANSION(d_uv)
22:
23:          // Spherical harmonics (covariant)
24:          if d_uv > ε then
25:             r̂_uv ← r_uv / d_uv  // Unit vector
26:             Y_uv ← SPHERICAL_HARMONICS(r̂_uv, l_max)
27:          else
28:             Y_uv ← ZEROS((l_max + 1)²)
29:          end if
30:
31:          // Combine node features for edge prediction
32:          h_pair ← concat(X_scalar[batch_idx, u], X_scalar[batch_idx, v], ψ_uv)
33:          h_edge ← EDGE_SCALAR_MLP(h_pair)
34:
35:          // Integrate spherical harmonics (maintaining equivariance)
36:          h_integrated ← SH_INTEGRATION_MLP(concat(h_edge, Y_uv))
37:
38:          // Predict edge existence
39:          p_edge ← EDGE_EXISTENCE_PREDICTOR(h_integrated)
40:
41:          if p_edge > threshold then
42:             // Generate edge attributes
43:             e_attr ← EDGE_ATTR_GENERATOR(h_integrated)
44:             Ê[batch_idx] ← Ê[batch_idx] ∪ {(u, v)}
45:             Ê_attr[batch_idx] ← Ê_attr[batch_idx] ∪ {e_attr}
46:          end if
47:       end for
48:    end for
49: end for

50: return P̂, Ê, Ê_attr
```

## Algorithm 7: E(3)-Equivariant Layer Normalization

```
Input: Scalar features X_scalar ∈ ℝ^{N×d}
       Vector features X_vector ∈ ℝ^{N×d×3} (optional)

Output: Normalized features X̃_scalar, X̃_vector

1: // Normalize scalar features
2: μ_scalar ← MEAN(X_scalar, dim=1)
3: σ²_scalar ← VAR(X_scalar, dim=1)
4: X̃_scalar ← γ_scalar ⊙ (X_scalar - μ_scalar)/√(σ²_scalar + ε) + β_scalar

5: if X_vector is provided then
6:    // Normalize vector magnitudes (preserve direction)
7:    ||X_vector|| ← ||X_vector||_2 along dim=2
8:    μ_vector ← MEAN(||X_vector||, dim=1)
9:    σ²_vector ← VAR(||X_vector||, dim=1)
10:   
11:   // Scale vectors while preserving equivariance
12:   scale ← γ_vector ⊙ ||X_vector|| / √(σ²_vector + ε)
13:   X̃_vector ← X_vector ⊙ scale.unsqueeze(-1)
14: end if

15: return X̃_scalar, X̃_vector
```

## Key Algorithmic Innovations

1. **Symmetric Capsule Architecture**: Both encoder and decoder use capsule networks with dynamic routing
2. **E(3)-Equivariant Generation**: Decoder maintains rotational symmetry through:
   - RBF expansion for distance encoding
   - Spherical harmonics for angular features
   - Equivariant position generation
   - Geometric-aware edge prediction
3. **Hierarchical Capsule Decoding**: Multi-level capsule transformation (latent → primary → secondary → geometric)
4. **Property-Conditioned Capsules**: Target properties directly influence capsule routing
5. **Geometric Integration**: Seamless combination of capsule representations with geometric operations
6. **Variational Capsule Framework**: Continuous latent space with capsule-enhanced generation

## Complexity Analysis

### Encoder Complexity
- **Convolution Layers**: O(L_conv × |E| × d_hidden² + |E| × l_max²)
- **Capsule Encoding**: O(R_enc × K_primary × K_secondary × d_capsule²)

### Decoder Complexity
- **Capsule Decoding**: O(R_dec × (K_latent × N_nodes + N_nodes²) × d_capsule²)
- **Geometric Generation**: O(N_nodes² × (d_rbf + l_max²) × d_hidden)
- **Edge Prediction**: O(N_nodes² × d_hidden²)

### Overall Complexity
- **Training Time**: O(L_conv × |E| × d_hidden² + R_total × N_caps² × d_capsule² + N_nodes² × d_hidden²)
- **Memory**: O(|V| × d_hidden + N_caps × d_capsule² + N_nodes² × d_hidden)
- **Generation Time**: O(N_samples × (R_dec × N_caps² × d_capsule² + N_nodes² × d_hidden²))

Where:
- L_conv: number of convolution layers
- |V|, |E|: number of nodes and edges in input graphs
- d_hidden: hidden dimension
- R_enc, R_dec, R_total: routing iterations (encoder, decoder, total)
- K_primary, K_secondary, N_caps: number of capsules at different levels
- d_capsule: capsule dimension
- N_nodes: maximum number of nodes in generated graphs
- d_rbf: number of radial basis functions
- l_max: maximum spherical harmonic degree
