"""
Test that all updated files work correctly with the enhanced encoder.
"""

import torch
import torch.nn as nn
from unittest.mock import Magic<PERSON>ock
import sys

# Mock the required layers for testing
class MockE3EquivariantCGCNNConv(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        self.linear = nn.Linear(channels, channels)
    
    def forward(self, x_scalar, x_vector, edge_index, edge_attr, pos):
        x_scalar_out = self.linear(x_scalar)
        x_vector_out = x_vector + 0.1 * torch.randn_like(x_vector)
        return x_scalar_out, x_vector_out

class MockE3EquivariantPrimaryCapsuleLayer(nn.Module):
    def __init__(self, scalar_features, vector_features, out_caps, caps_dim):
        super().__init__()
        self.out_caps = out_caps
        self.caps_dim = caps_dim
        self.projection = nn.Linear(scalar_features, out_caps * caps_dim)
    
    def forward(self, x_scalar, x_vector):
        batch_size = x_scalar.size(0)
        caps = self.projection(x_scalar).view(batch_size, self.out_caps, self.caps_dim)
        vectors = x_vector[:, :self.out_caps, :]
        return caps, vectors

class MockE3LayerNorm(nn.Module):
    def __init__(self, channels):
        super().__init__()
        self.norm = nn.LayerNorm(channels)
    
    def forward(self, x_scalar, x_vector):
        return self.norm(x_scalar), x_vector

class MockData:
    """Mock PyTorch Geometric Data object."""
    def __init__(self, batch_size=2, num_nodes_per_graph=[8, 6]):
        self.batch_size = batch_size
        self.num_nodes_per_graph = num_nodes_per_graph
        total_nodes = sum(num_nodes_per_graph)
        
        # Create mock data
        self.x = torch.randn(total_nodes, 10)  # node features
        self.pos = torch.randn(total_nodes, 3)  # positions
        self.batch = torch.cat([torch.full((n,), i) for i, n in enumerate(num_nodes_per_graph)])
        
        # Create simple edges (chain for each graph)
        edge_indices = []
        edge_attrs = []
        node_offset = 0
        
        for i, num_nodes in enumerate(num_nodes_per_graph):
            if num_nodes > 1:
                edges = torch.stack([
                    torch.arange(num_nodes - 1) + node_offset,
                    torch.arange(1, num_nodes) + node_offset
                ])
                edge_indices.append(edges)
                edge_attrs.append(torch.randn(num_nodes - 1, 2))
            node_offset += num_nodes
        
        self.edge_index = torch.cat(edge_indices, dim=1)
        self.edge_attr = torch.cat(edge_attrs, dim=0)

# Mock the imports
mock_layers = MagicMock()
mock_layers.E3EquivariantCGCNNConv = MockE3EquivariantCGCNNConv
mock_layers.E3EquivariantPrimaryCapsuleLayer = MockE3EquivariantPrimaryCapsuleLayer
mock_layers.E3LayerNorm = MockE3LayerNorm

sys.modules['layers'] = mock_layers

# Import the models after mocking
from models import EncoderGNN, CrystalVAE, CapsuleDecoderLayer

def test_enhanced_encoder_integration():
    """Test the enhanced encoder integration with CrystalVAE."""
    print("Testing Enhanced Encoder Integration with CrystalVAE...")
    
    # Model parameters
    node_features = 10
    edge_features = 2
    hidden_channels = 64
    latent_dim = 32
    num_conv_layers = 2
    batch_size = 2
    
    try:
        # Create CrystalVAE with enhanced encoder
        model = CrystalVAE(
            node_features=node_features,
            edge_features=edge_features,
            hidden_channels=hidden_channels,
            latent_dim=latent_dim,
            num_conv_layers=num_conv_layers,
            kl_weight=0.01,
            device='cpu'
        )
        
        print(f"✅ CrystalVAE created successfully with enhanced encoder")
        print(f"   - Encoder type: {type(model.encoder).__name__}")
        print(f"   - Latent projections: {model.mu_projection.in_features} -> {model.mu_projection.out_features}")
        
        # Verify encoder architecture
        encoder = model.encoder
        print(f"\n📊 Enhanced Encoder Architecture:")
        print(f"   - Primary encoder caps: {encoder.primary_encoder_caps}")
        print(f"   - Secondary encoder caps: {encoder.secondary_encoder_caps}")
        print(f"   - Both use CapsuleDecoderLayer: {isinstance(encoder.primary_encoder_caps, CapsuleDecoderLayer)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating CrystalVAE: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_full_vae_pipeline():
    """Test the complete VAE pipeline with enhanced encoder."""
    print("\nTesting Full VAE Pipeline with Enhanced Encoder...")
    
    try:
        # Create model
        model = CrystalVAE(
            node_features=10,
            edge_features=2,
            hidden_channels=64,
            latent_dim=32,
            num_conv_layers=2,
            kl_weight=0.01,
            device='cpu'
        )
        
        # Create test data
        data = MockData(batch_size=2, num_nodes_per_graph=[8, 6])
        
        print(f"✅ Test data created:")
        print(f"   - Batch size: {data.batch_size}")
        print(f"   - Total nodes: {data.x.size(0)}")
        print(f"   - Node features: {data.x.size(1)}")
        print(f"   - Edge features: {data.edge_attr.size(1)}")
        
        # Test encoding
        print(f"\n🔄 Testing encoding...")
        mu, logvar = model.encode(data)
        print(f"   - Encoder output mu: {mu.shape}")
        print(f"   - Encoder output logvar: {logvar.shape}")
        
        # Test reparameterization
        print(f"\n🔄 Testing reparameterization...")
        z = model.reparameterize(mu, logvar)
        print(f"   - Latent representation z: {z.shape}")
        
        # Test property prediction
        print(f"\n🔄 Testing property prediction...")
        pred_property = model.property_predictor(z)
        print(f"   - Predicted property: {pred_property.shape}")
        
        print(f"\n✅ Full VAE pipeline successful!")
        print(f"   - Enhanced encoder working correctly")
        print(f"   - Latent space dimensions: {z.shape}")
        print(f"   - Property prediction: {pred_property.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in VAE pipeline: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_encoder_output_dimensions():
    """Test that encoder outputs correct dimensions for latent projection."""
    print("\nTesting Encoder Output Dimensions...")
    
    try:
        # Create encoder
        encoder = EncoderGNN(
            node_features=10,
            edge_features=2,
            hidden_channels=64,
            num_conv_layers=2
        )
        
        # Create test data
        data = MockData(batch_size=3, num_nodes_per_graph=[5, 8, 4])
        
        # Forward pass
        output = encoder(data)
        
        print(f"✅ Encoder forward pass successful:")
        print(f"   - Input: {data.x.size(0)} nodes across {data.batch_size} graphs")
        print(f"   - Output shape: {output.shape}")
        print(f"   - Expected: (3, 6, 16)")
        
        # Test flattening for latent projection
        flattened = output.view(output.size(0), -1)
        print(f"   - Flattened shape: {flattened.shape}")
        print(f"   - Expected: (3, 96)")
        
        # Verify dimensions match CrystalVAE expectations
        expected_flat_dim = 6 * 16  # 96
        actual_flat_dim = flattened.size(1)
        
        if actual_flat_dim == expected_flat_dim:
            print(f"✅ Dimensions match CrystalVAE expectations!")
            return True
        else:
            print(f"❌ Dimension mismatch! Expected {expected_flat_dim}, got {actual_flat_dim}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing dimensions: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_architectural_symmetry():
    """Test that encoder and decoder have symmetric capsule architectures."""
    print("\nTesting Architectural Symmetry...")
    
    try:
        # Create model
        model = CrystalVAE(
            node_features=10,
            edge_features=2,
            hidden_channels=64,
            latent_dim=32,
            num_conv_layers=2,
            device='cpu'
        )
        
        encoder = model.encoder
        
        print(f"📊 Encoder Capsule Architecture:")
        print(f"   - Primary: {type(encoder.primary_encoder_caps).__name__}")
        print(f"   - Secondary: {type(encoder.secondary_encoder_caps).__name__}")
        
        # Check if both use CapsuleDecoderLayer
        primary_sophisticated = isinstance(encoder.primary_encoder_caps, CapsuleDecoderLayer)
        secondary_sophisticated = isinstance(encoder.secondary_encoder_caps, CapsuleDecoderLayer)
        
        print(f"\n🔧 Sophistication Check:")
        print(f"   - Primary uses CapsuleDecoderLayer: {primary_sophisticated}")
        print(f"   - Secondary uses CapsuleDecoderLayer: {secondary_sophisticated}")
        
        if primary_sophisticated and secondary_sophisticated:
            print(f"\n🎉 SUCCESS: Perfect architectural symmetry achieved!")
            print(f"   - Both encoder and decoder use sophisticated capsule layers")
            print(f"   - 4D transformation matrices throughout")
            print(f"   - Dynamic routing in both directions")
            return True
        else:
            print(f"\n❌ FAILURE: Architectural asymmetry detected")
            return False
            
    except Exception as e:
        print(f"❌ Error testing symmetry: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 80)
    print("TESTING UPDATED FILES WITH ENHANCED ENCODER")
    print("=" * 80)
    
    test1_passed = test_enhanced_encoder_integration()
    test2_passed = test_full_vae_pipeline()
    test3_passed = test_encoder_output_dimensions()
    test4_passed = test_architectural_symmetry()
    
    print("\n" + "=" * 80)
    if all([test1_passed, test2_passed, test3_passed, test4_passed]):
        print("🎉 ALL TESTS PASSED!")
        print("✅ Enhanced encoder integrates perfectly with existing code")
        print("✅ CrystalVAE works correctly with sophisticated encoder")
        print("✅ Output dimensions are correct for latent projection")
        print("✅ Perfect architectural symmetry achieved")
        print("✅ All updated files are compatible")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    print("=" * 80)
