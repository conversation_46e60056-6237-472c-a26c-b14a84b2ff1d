"""
Training utilities for Crystal VAE.

This module contains functions for training the Crystal VAE model,
including the main training loop, evaluation, and visualization utilities.
"""

import os
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader, random_split
from torch_geometric.data import Batch
from tqdm.notebook import tqdm
from data import CartesianGraphDataset
from models import CrystalVAE


def train_crystal_vae(model, train_loader, val_loader, optimizer, device, epochs=100,
                     scheduler=None, checkpoint_path="best_vae_model.pt",
                     early_stopping_patience=20):
    """
    Train the Crystal VAE model.
    
    Args:
        model: CrystalVAE model instance
        train_loader: Training data loader
        val_loader: Validation data loader
        optimizer: Optimizer instance
        device: Device to use for training
        epochs: Number of training epochs
        scheduler: Learning rate scheduler (optional)
        checkpoint_path: Path to save best model
        early_stopping_patience: Patience for early stopping
        
    Returns:
        tuple: (trained_model, training_history)
    """
    model.train()
    best_loss = float('inf')
    patience_counter = 0

    history = {
        'epoch': [],
        'train_loss': [],
        'val_loss': [],
        'node_recon': [],
        'pos_recon': [],
        'edge_recon': [],
        'prop_loss': [],
        'kl_loss': []
    }

    checkpoint_dir = os.path.dirname(checkpoint_path)
    os.makedirs(checkpoint_dir, exist_ok=True)
    log_path = os.path.join(checkpoint_dir, "training_log.csv")

    print(f"Training on {device} for {epochs} epochs")
    print(f"Checkpoints will be saved to {checkpoint_path}")

    for epoch in range(epochs):
        model.train()
        epoch_loss = 0

        loss_components = {
            'node_recon': 0,
            'pos_recon': 0,
            'edge_recon': 0,
            'prop_loss': 0,
            'kl_loss': 0
        }

        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} [Train]")

        for batch in progress_bar:
            batch = batch.to(device)
            optimizer.zero_grad()

            # Forward pass - model returns a dictionary
            output = model(batch)

            # Extract outputs from dictionary
            node_features = output['node_features']
            positions = output['positions']
            edge_indices = output['edge_indices']
            edge_attrs = output['edge_attrs']
            pred_property = output['pred_property']
            mu = output['mu']
            logvar = output['logvar']

            # Compute loss
            loss, components = model.loss_function(
                batch, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar
            )

            loss.backward()
            optimizer.step()

            progress_bar.set_postfix(loss=f"{loss.item():.4f}")

            epoch_loss += loss.item()
            for k, v in components.items():
                loss_components[k] += v

        avg_train_loss = epoch_loss / len(train_loader)
        for k in loss_components:
            loss_components[k] /= len(train_loader)

        # Validation
        model.eval()
        val_loss = 0
        progress_bar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} [Val]")

        with torch.no_grad():
            for batch in progress_bar:
                batch = batch.to(device)

                # Forward pass
                output = model(batch)

                # Extract outputs
                node_features = output['node_features']
                positions = output['positions']
                edge_indices = output['edge_indices']
                edge_attrs = output['edge_attrs']
                pred_property = output['pred_property']
                mu = output['mu']
                logvar = output['logvar']

                loss, _ = model.loss_function(
                    batch, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar
                )

                val_loss += loss.item()
                progress_bar.set_postfix(loss=f"{loss.item():.4f}")

        avg_val_loss = val_loss / len(val_loader)

        # Update history
        history['epoch'].append(epoch+1)
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        for k in loss_components:
            history[k].append(loss_components[k])

        print(f"[Epoch {epoch+1}/{epochs}] Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f} | "
              f"Node: {loss_components['node_recon']:.4f}, "
              f"Pos: {loss_components['pos_recon']:.4f}, "
              f"Prop: {loss_components['prop_loss']:.4f}, "
              f"KL: {loss_components['kl_loss']:.4f}")

        # Save log header on first epoch
        if epoch == 0:
            with open(log_path, 'w') as f:
                f.write(','.join(['epoch', 'train_loss', 'val_loss'] + list(loss_components.keys())) + '\n')

        # Append to log
        with open(log_path, 'a') as f:
            f.write(','.join([str(epoch+1), f"{avg_train_loss:.6f}", f"{avg_val_loss:.6f}"] +
                            [f"{loss_components[k]:.6f}" for k in loss_components]) + '\n')

        # Learning rate scheduling
        if scheduler:
            scheduler.step(avg_val_loss)

        # Early stopping and checkpointing
        if avg_val_loss < best_loss:
            best_loss = avg_val_loss
            patience_counter = 0

            # Save best model
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_train_loss,
                'val_loss': best_loss,
                'history': history,
                'model_config': {
                    'node_features': model.decoder.node_features,
                    'edge_features': model.decoder.edge_features,
                    'hidden_channels': model.decoder.hidden_channels,
                    'latent_dim': model.latent_dim,
                }
            }, checkpoint_path)
            print(f"✓ Saved best model at epoch {epoch+1} (val_loss: {best_loss:.4f})")

            # Backup every 10 epochs
            if (epoch + 1) % 10 == 0:
                backup_path = checkpoint_path.replace('.pt', f'_epoch{epoch+1}.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': avg_train_loss,
                    'val_loss': avg_val_loss,
                    'history': history,
                    'model_config': {
                        'node_features': model.decoder.node_features,
                        'edge_features': model.decoder.edge_features,
                        'hidden_channels': model.decoder.hidden_channels,
                        'latent_dim': model.latent_dim,
                    }
                }, backup_path)
        else:
            patience_counter += 1
            if patience_counter >= early_stopping_patience:
                print(f"Early stopping triggered after {patience_counter} epochs without improvement")
                break

        # Plot training curves every 10 epochs
        if (epoch + 1) % 10 == 0 or epoch == epochs - 1:
            try:
                import matplotlib.pyplot as plt
                plot_training_curves(history, checkpoint_dir, epoch+1)
                print(f"✓ Saved loss plot at epoch {epoch+1}")
            except Exception as e:
                print(f"Could not generate loss plot: {str(e)}")

    return model, history


def plot_training_curves(history, output_dir, epoch):
    """
    Plot training curves.
    
    Args:
        history: Training history dictionary
        output_dir: Directory to save plots
        epoch: Current epoch number
    """
    import matplotlib.pyplot as plt

    plt.figure(figsize=(15, 10))

    # Plot train and validation loss
    plt.subplot(2, 2, 1)
    plt.plot(history['epoch'], history['train_loss'], 'b-', label='Train Loss')
    plt.plot(history['epoch'], history['val_loss'], 'r-', label='Validation Loss')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot property prediction loss
    plt.subplot(2, 2, 2)
    plt.plot(history['epoch'], history['prop_loss'], 'g-', label='Property Prediction Loss')
    plt.title('Property Prediction Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot reconstruction losses
    plt.subplot(2, 2, 3)
    plt.plot(history['epoch'], history['node_recon'], label='Node Reconstruction')
    plt.plot(history['epoch'], history['pos_recon'], label='Position Reconstruction')
    plt.plot(history['epoch'], history['edge_recon'], label='Edge Reconstruction')
    plt.title('Reconstruction Losses')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot KL loss
    plt.subplot(2, 2, 4)
    plt.plot(history['epoch'], history['kl_loss'], 'm-', label='KL Divergence')
    plt.title('KL Divergence Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"loss_plot_epoch_{epoch}.png"))
    plt.close()


def run_crystal_vae(dataset_path, target_name, epochs=100, batch_size=32,
                   hidden_channels=128, latent_dim=64, num_conv_layers=3,
                   kl_weight=0.01, learning_rate=1e-4, pretrained_model_path=None):
    """
    Main function to run crystal VAE training.

    Args:
        dataset_path: Path to dataset directory
        target_name: Name of target property
        epochs: Number of training epochs
        batch_size: Batch size for training
        hidden_channels: Number of hidden channels
        latent_dim: Latent space dimension
        num_conv_layers: Number of convolution layers
        kl_weight: Weight for KL divergence loss
        learning_rate: Learning rate for optimizer
        pretrained_model_path: Path to pretrained model (optional)

    Returns:
        tuple: (trained_model, training_history)
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Create output directory
    output_dir = f"crystal_vae_{target_name}"
    os.makedirs(output_dir, exist_ok=True)
    checkpoint_path = os.path.join(output_dir, f"best_crystal_vae_{target_name}.pt")

    print("=" * 50)
    print(f"Crystal VAE Training Configuration:")
    print(f"- Target property: {target_name}")
    print(f"- Epochs: {epochs}")
    print(f"- Batch size: {batch_size}")
    print(f"- Hidden channels: {hidden_channels}")
    print(f"- Latent dimension: {latent_dim}")
    print(f"- Conv layers: {num_conv_layers}")
    print(f"- KL weight: {kl_weight}")
    print(f"- Learning rate: {learning_rate}")
    print(f"- Device: {device}")
    print(f"- Output directory: {output_dir}")
    print("=" * 50)

    # Load dataset
    print("Loading dataset...")
    dataset = CartesianGraphDataset(dataset_path, target_name=target_name)
    print(f"Dataset loaded with {len(dataset)} samples")

    # Split dataset
    train_size = int(0.8 * len(dataset))
    val_size = int(0.1 * len(dataset))
    test_size = len(dataset) - train_size - val_size

    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size]
    )

    print(f"Split: {train_size} train, {val_size} validation, {test_size} test")

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        collate_fn=Batch.from_data_list
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        num_workers=4,
        pin_memory=True,
        collate_fn=Batch.from_data_list
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        num_workers=4,
        pin_memory=True,
        collate_fn=Batch.from_data_list
    )

    # Initialize model
    print("Initializing Crystal VAE model")
    model = CrystalVAE(
        node_features=dataset[0].x.size(1),
        edge_features=dataset[0].edge_attr.size(1),
        hidden_channels=hidden_channels,
        latent_dim=latent_dim,
        num_conv_layers=num_conv_layers,
        kl_weight=kl_weight,
        device=device
    ).to(device)

    print(f"Model parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Load pretrained model if provided
    if pretrained_model_path and os.path.exists(pretrained_model_path):
        print(f"Loading pretrained model from {pretrained_model_path}")
        checkpoint = torch.load(pretrained_model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("Pretrained model loaded successfully.")

    # Setup optimizer and scheduler
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )

    # Train model
    print(f"Starting training for {epochs} epochs...")
    model, history = train_crystal_vae(
        model, train_loader, val_loader, optimizer, device,
        epochs=epochs,
        scheduler=scheduler,
        checkpoint_path=checkpoint_path,
        early_stopping_patience=20
    )

    # Save training history
    np.savez(os.path.join(output_dir, "training_history.npz"), **history)
    print(f"✓ Saved training history to {output_dir}/training_history.npz")

    # Evaluate on test set
    print("Evaluating model on test set...")
    model.eval()
    test_loss = 0
    property_errors = []

    with torch.no_grad():
        for batch in test_loader:
            batch = batch.to(device)
            output = model(batch)

            node_features = output['node_features']
            positions = output['positions']
            edge_indices = output['edge_indices']
            edge_attrs = output['edge_attrs']
            pred_property = output['pred_property']
            mu = output['mu']
            logvar = output['logvar']

            loss, _ = model.loss_function(
                batch, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar
            )
            test_loss += loss.item()

            property_errors.append((pred_property - batch.y).abs().cpu().numpy())

    avg_test_loss = test_loss / len(test_loader)
    property_errors = np.concatenate(property_errors)
    mean_abs_error = property_errors.mean()

    print(f"Test Loss: {avg_test_loss:.4f}")
    print(f"Mean Absolute Error for {target_name}: {mean_abs_error:.4f}")

    # Save test results
    with open(os.path.join(output_dir, "test_results.txt"), "w") as f:
        f.write(f"Test Loss: {avg_test_loss:.6f}\n")
        f.write(f"Mean Absolute Error for {target_name}: {mean_abs_error:.6f}\n")

    print(f"Model training complete. You can now use this model to generate crystal structures with specific {target_name} values.")
    print(f"To generate structures, load the saved model from {checkpoint_path} and use the model.generate() method.")

    return model, history
