# Core dependencies for Crystal VAE
torch>=1.12.0
torch-geometric>=2.1.0
torch-scatter>=2.0.9
torch-sparse>=0.6.15

# Scientific computing
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# E(3) equivariant neural networks
e3nn>=0.5.0

# Progress bars and utilities
tqdm>=4.62.0

# Visualization (optional)
matplotlib>=3.5.0

# Data handling
h5py>=3.7.0

# Development and testing (optional)
pytest>=6.2.0
jupyter>=1.0.0
ipykernel>=6.0.0

# Additional utilities
scipy>=1.7.0
seaborn>=0.11.0  # For enhanced plotting

# GPU support (uncomment if using CUDA)
# torch-audio  # For CUDA support
# torchvision  # For CUDA support

# Optional: For advanced visualization
# plotly>=5.0.0
# mayavi>=4.7.0  # For 3D visualization

# Optional: For molecular/crystal structure analysis
# pymatgen>=2022.0.0  # Materials analysis
# ase>=3.22.0  # Atomic simulation environment
