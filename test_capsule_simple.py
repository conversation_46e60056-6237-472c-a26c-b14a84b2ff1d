"""
Simple test for CapsuleDecoderLayer without external dependencies.
"""

import torch
import torch.nn as nn

class CapsuleDecoderLayer(nn.Module):
    """
    Capsule decoder layer with dynamic routing for hierarchical transformations.
    """
    
    def __init__(self, in_caps, in_dim, out_caps, out_dim, routing_iterations=3):
        super(CapsuleDecoderLayer, self).__init__()
        self.in_caps = in_caps
        self.in_dim = in_dim
        self.out_caps = out_caps
        self.out_dim = out_dim
        self.routing_iterations = routing_iterations

        # Transformation matrices for capsule routing
        self.W = nn.Parameter(torch.randn(out_caps, in_caps, in_dim, out_dim))
        self.bias = nn.Parameter(torch.zeros(out_caps, out_dim))

    def squash(self, tensor, dim=-1):
        """
        Squashing function to ensure capsule vectors have appropriate magnitude.
        """
        squared_norm = (tensor ** 2).sum(dim=dim, keepdim=True)
        scale = squared_norm / (1 + squared_norm)
        return scale * tensor / torch.sqrt(squared_norm + 1e-8)

    def forward(self, input_capsules):
        """
        Forward pass with dynamic routing.
        
        Args:
            input_capsules (torch.Tensor): Input capsules [batch_size, in_caps, in_dim]
            
        Returns:
            torch.Tensor: Output capsules [batch_size, out_caps, out_dim]
        """
        batch_size = input_capsules.size(0)
        
        # Compute prediction vectors using efficient tensor operations
        # input_capsules: [batch_size, in_caps, in_dim]
        # self.W: [out_caps, in_caps, in_dim, out_dim]
        # We want u_hat: [batch_size, in_caps, out_caps, out_dim]
        
        # Perform batched matrix multiplication using einsum
        # b=batch, i=in_caps, d=in_dim, o=out_caps, e=out_dim
        u_hat = torch.einsum('bid,oide->bioe', input_capsules, self.W)  # [batch_size, in_caps, out_caps, out_dim]

        # Dynamic routing
        b_ij = torch.zeros(batch_size, self.in_caps, self.out_caps, device=input_capsules.device)

        for iteration in range(self.routing_iterations):
            # Softmax over output capsules
            c_ij = torch.softmax(b_ij, dim=2)  # [batch_size, in_caps, out_caps]

            # Weighted sum of prediction vectors
            s_j = torch.sum(c_ij.unsqueeze(-1) * u_hat, dim=1)  # [batch_size, out_caps, out_dim]

            # Apply squashing
            v_j = self.squash(s_j, dim=-1)  # [batch_size, out_caps, out_dim]

            # Update routing coefficients (except for last iteration)
            if iteration < self.routing_iterations - 1:
                # Agreement between prediction and output
                agreement = torch.sum(u_hat * v_j.unsqueeze(1), dim=-1)  # [batch_size, in_caps, out_caps]
                b_ij = b_ij + agreement

        # Add bias
        output_capsules = v_j + self.bias.unsqueeze(0)  # [batch_size, out_caps, out_dim]
        
        return output_capsules

def test_capsule_decoder_dimensions():
    """Test that the CapsuleDecoderLayer works with correct dimensions."""
    print("Testing CapsuleDecoderLayer dimension fix...")
    
    # Test parameters
    batch_size = 2
    in_caps = 8
    in_dim = 16
    out_caps = 10
    out_dim = 32
    
    # Create layer
    layer = CapsuleDecoderLayer(in_caps, in_dim, out_caps, out_dim, routing_iterations=3)
    
    # Create test input
    input_capsules = torch.randn(batch_size, in_caps, in_dim)
    
    print(f"Input shape: {input_capsules.shape}")
    print(f"Expected output shape: ({batch_size}, {out_caps}, {out_dim})")
    
    try:
        # Forward pass
        output_capsules = layer(input_capsules)
        print(f"✅ Success! Output shape: {output_capsules.shape}")
        print(f"Output range: [{output_capsules.min():.3f}, {output_capsules.max():.3f}]")
        
        # Verify shape
        expected_shape = (batch_size, out_caps, out_dim)
        if output_capsules.shape == expected_shape:
            print(f"✅ Shape verification passed!")
            return True
        else:
            print(f"❌ Shape mismatch! Expected {expected_shape}, got {output_capsules.shape}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_different_dimensions():
    """Test with different dimension combinations."""
    print("\nTesting different dimension combinations...")
    
    test_cases = [
        (1, 4, 8, 6, 16),   # Small case
        (3, 8, 16, 10, 32), # Medium case
        (2, 16, 32, 12, 24) # Larger case
    ]
    
    passed = 0
    for i, (batch_size, in_caps, in_dim, out_caps, out_dim) in enumerate(test_cases):
        print(f"Test case {i+1}: batch={batch_size}, in_caps={in_caps}, in_dim={in_dim}, out_caps={out_caps}, out_dim={out_dim}")
        
        try:
            layer = CapsuleDecoderLayer(in_caps, in_dim, out_caps, out_dim, routing_iterations=2)
            input_capsules = torch.randn(batch_size, in_caps, in_dim)
            output_capsules = layer(input_capsules)
            
            expected_shape = (batch_size, out_caps, out_dim)
            if output_capsules.shape == expected_shape:
                print(f"  ✅ Passed! Output shape: {output_capsules.shape}")
                passed += 1
            else:
                print(f"  ❌ Failed! Expected {expected_shape}, got {output_capsules.shape}")
                
        except Exception as e:
            print(f"  ❌ Error: {str(e)}")
    
    print(f"Passed {passed}/{len(test_cases)} test cases")
    return passed == len(test_cases)

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Capsule Decoder Dimension Fix (Standalone)")
    print("=" * 60)
    
    test1_passed = test_capsule_decoder_dimensions()
    test2_passed = test_different_dimensions()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 All tests passed! The dimension fix is working correctly.")
        print("The CapsuleDecoderLayer can now handle the tensor operations properly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    print("=" * 60)
