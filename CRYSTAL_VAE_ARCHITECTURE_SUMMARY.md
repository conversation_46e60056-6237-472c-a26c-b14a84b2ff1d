# Crystal VAE: Complete Architecture Summary with Dimensions

## Overview
The Crystal VAE is a **sophisticated capsule-enhanced E(3)-equivariant variational autoencoder** for crystal structure generation. Both encoder and decoder use advanced capsule networks with dynamic routing and 4D transformation matrices, ensuring perfect architectural symmetry and geometric consistency.

## Model Architecture Flow

### Input Data Dimensions
```
Input Graph:
├── Node features (x): [num_nodes, node_features] 
├── Edge features (edge_attr): [num_edges, edge_features]
├── Edge indices (edge_index): [2, num_edges]
├── Positions (pos): [num_nodes, 3]
└── Batch assignment: [num_nodes] (for batched processing)

Typical Values:
├── node_features = 10 (atomic numbers, etc.)
├── edge_features = 2 (bond types, distances)
├── num_nodes = variable per crystal
└── num_edges = variable per crystal
```

## 1. ENCODER ARCHITECTURE

### 1.1 Node & Edge Embedding
```python
# Input Processing
x_with_pos = concat([x, pos])           # [num_nodes, node_features + 3]
x_embedded = node_embedding(x_with_pos) # [num_nodes, hidden_channels]
edge_embedded = edge_embedding(edge_attr) # [num_edges, hidden_channels]

# Initialize Features
x_scalar = x_embedded                   # [num_nodes, hidden_channels]
x_vector = pos.repeat(1, hidden_channels, 1) # [num_nodes, hidden_channels, 3]
```

### 1.2 E(3)-Equivariant Convolution Layers
```python
for layer in range(num_conv_layers):
    # E(3)-Equivariant CGCNN Convolution
    x_scalar, x_vector = conv_layer(
        x_scalar,    # [num_nodes, hidden_channels]
        x_vector,    # [num_nodes, hidden_channels, 3]
        edge_index,  # [2, num_edges]
        edge_attr,   # [num_edges, hidden_channels]
        pos          # [num_nodes, 3]
    )
    # Output: x_scalar [num_nodes, hidden_channels]
    #         x_vector [num_nodes, hidden_channels, 3]
```

### 1.3 Sophisticated Capsule Network Encoding
```python
# E(3)-Equivariant Primary Capsules
primary_caps, primary_vectors = primary_caps_layer(
    x_scalar,  # [num_nodes, hidden_channels]
    x_vector   # [num_nodes, hidden_channels, 3]
)
# Output: primary_caps [num_nodes, 16, 32]
#         primary_vectors [num_nodes, 16, 32, 3]

# Batch Aggregation (Global Pooling)
batched_primary_caps = global_mean_pool_by_batch(primary_caps, batch)
# Output: batched_primary_caps [batch_size, 16, 32]

# Sophisticated Primary Encoder Capsules (CapsuleDecoderLayer)
refined_caps = primary_encoder_caps(batched_primary_caps)
# Input:  [batch_size, 16, 32]
# Output: [batch_size, 12, 24]
# Features: 4D transformation matrix W[12, 16, 32, 24], dynamic routing

# Sophisticated Secondary Encoder Capsules (CapsuleDecoderLayer)
secondary_caps = secondary_encoder_caps(refined_caps)
# Input:  [batch_size, 12, 24]
# Output: [batch_size, 6, 16]
# Features: 4D transformation matrix W[6, 12, 24, 16], dynamic routing
```

### 1.4 Latent Space Projection
```python
# Flatten capsules
h = secondary_caps.flatten(1)  # [batch_size, 6 * 16] = [batch_size, 96]

# Project to latent space
mu = mu_projection(h)          # [batch_size, latent_dim]
logvar = logvar_projection(h)  # [batch_size, latent_dim]

# Reparameterization
z = mu + exp(0.5 * logvar) * epsilon  # [batch_size, latent_dim]
```

## 2. DECODER ARCHITECTURE

### 2.1 Latent Conditioning
```python
# Condition with target property
z_cond = concat([z, target_property])  # [batch_size, latent_dim + 1]

# Expand to initial capsules
latent_caps = latent_to_capsules(z_cond)  # [batch_size, 8 * 16] = [batch_size, 128]
latent_caps = latent_caps.view(batch_size, 8, 16)  # [batch_size, 8, 16]
```

### 2.2 Hierarchical Capsule Decoding

#### Primary Decoder Capsules
```python
node_caps_primary = primary_decoder_caps(latent_caps)
# Input:  [batch_size, 8, 16]
# Output: [batch_size, max_nodes, 32]

# CapsuleDecoderLayer Internal Dimensions:
# - Transformation matrix W: [max_nodes, 8, 16, 32]
# - Prediction vectors u_hat: [batch_size, 8, max_nodes, 32]
# - Routing coefficients b_ij: [batch_size, 8, max_nodes]
# - Output capsules: [batch_size, max_nodes, 32]
```

#### Secondary Decoder Capsules
```python
node_caps_refined = secondary_decoder_caps(node_caps_primary)
# Input:  [batch_size, max_nodes, 32]
# Output: [batch_size, max_nodes, hidden_channels]

# CapsuleDecoderLayer Internal Dimensions:
# - Transformation matrix W: [max_nodes, max_nodes, 32, hidden_channels]
# - Prediction vectors u_hat: [batch_size, max_nodes, max_nodes, hidden_channels]
# - Routing coefficients b_ij: [batch_size, max_nodes, max_nodes]
# - Output capsules: [batch_size, max_nodes, hidden_channels]
```

### 2.3 E(3)-Equivariant Geometric Generation
```python
positions, edge_indices, edge_probs, edge_attrs = geometric_decoder(node_caps_refined)

# Internal Processing:
# 1. Capsule to scalar features
scalar_features = caps_to_scalar(node_caps_refined)  # [batch_size, max_nodes, hidden_channels]

# 2. Position generation (equivariant)
positions = position_mlp(scalar_features)  # [batch_size, max_nodes, 3]

# 3. Edge generation (for each pair of nodes)
for i, j in all_node_pairs:
    # Distance and direction
    r_ij = positions[:, i] - positions[:, j]  # [batch_size, 3]
    d_ij = norm(r_ij)                         # [batch_size]
    
    # RBF expansion
    rbf_features = rbf_layer(d_ij)            # [batch_size, num_rbf]
    
    # Spherical harmonics (if d_ij > threshold)
    sh_features = spherical_harmonics(r_ij/d_ij)  # [batch_size, (lmax+1)²]
    
    # Combine features
    edge_features = concat([rbf_features, sh_features])  # [batch_size, num_rbf + (lmax+1)²]
    
    # Edge probability and attributes
    edge_prob = edge_prob_mlp(edge_features)  # [batch_size, 1]
    edge_attr = edge_attr_mlp(edge_features)  # [batch_size, edge_features]

# Output Lists (per batch sample):
# - positions: [batch_size, num_nodes, 3]
# - edge_indices: List of [2, num_edges_i] for each sample i
# - edge_probs: List of [num_edges_i] for each sample i  
# - edge_attrs: List of [num_edges_i, edge_features] for each sample i
```

### 2.4 Final Node Feature Generation
```python
node_features_batch = node_feature_generator(node_caps_refined)
# Input:  [batch_size, max_nodes, hidden_channels]
# Output: [batch_size, max_nodes, node_features]

# Split into list per batch sample
node_features_list = [node_features_batch[i, :num_nodes] for i in range(batch_size)]
# Output: List of [num_nodes_i, node_features] for each sample i
```

## 3. COMPLETE MODEL DIMENSIONS

### Default Hyperparameters
```python
node_features = 10          # Input/output node feature dimension
edge_features = 2           # Input/output edge feature dimension  
hidden_channels = 128       # Hidden layer dimension throughout model
latent_dim = 32            # Latent space dimension
num_conv_layers = 2        # Number of E(3)-equivariant convolution layers
max_nodes = 50             # Maximum nodes in generated structures
```

### Memory Complexity
```python
# Encoder Memory:
# - Node embeddings: O(num_nodes × hidden_channels)
# - Convolution layers: O(num_edges × hidden_channels²)
# - Primary capsules: O(batch_size × 8 × 32)
# - Secondary capsules: O(batch_size × 6 × 16)

# Decoder Memory:
# - Capsule transformations: O(max_nodes² × hidden_channels²)
# - Geometric generation: O(max_nodes² × (num_rbf + (lmax+1)²))
# - Edge prediction: O(max_nodes² × hidden_channels)

# Total Model Parameters:
# - Encoder: ~500K parameters
# - Decoder: ~2M parameters (dominated by capsule transformation matrices)
# - Total: ~2.5M parameters
```

## 4. KEY ARCHITECTURAL FEATURES

### Symmetric Capsule Design
- **Encoder**: Uses capsule networks for hierarchical feature extraction
- **Decoder**: Uses capsule networks for hierarchical structure generation
- **Dynamic Routing**: Both encoder and decoder employ iterative routing algorithms

### E(3)-Equivariance
- **RBF Expansion**: Distance-based features preserve translation invariance
- **Spherical Harmonics**: Angular features preserve rotation equivariance  
- **Geometric Operations**: All spatial operations maintain symmetries

### Hierarchical Processing
- **Encoder**: Raw features → Convolutions → Primary Capsules → Secondary Capsules → Latent
- **Decoder**: Latent → Primary Capsules → Secondary Capsules → Geometric Features → Structure

### Property Conditioning
- **Target Property**: Injected at latent conditioning stage
- **Guided Generation**: Properties influence all subsequent capsule transformations

This architecture ensures that generated crystal structures maintain physical realism while being conditioned on desired material properties.
