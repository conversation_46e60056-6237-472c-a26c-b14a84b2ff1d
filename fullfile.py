import os
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from tqdm.notebook import tqdm
from torch.utils.data import Dataset, DataLoader, SubsetRandomSampler, random_split
from torch_geometric.nn import MessagePassing
from torch_geometric.data import Data, Batch
from torch_geometric.utils import scatter, to_dense_batch
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.metrics import mean_absolute_error
from torch.optim.lr_scheduler import ReduceLROnPlateau
import torch_geometric.transforms as T
from e3nn import o3
from e3nn.o3 import FullyConnectedTensorProduct
from e3nn.nn import Gate, BatchNorm
from e3nn.o3 import Irreps, spherical_harmonics

import warnings


warnings.filterwarnings('ignore')

class Graph:
    def __init__(self, graph_data):
        try:
            self.nodes = graph_data['node_features']
            self.type_counts = graph_data['type_counts']
            self.neighbor_counts = graph_data['neighbor_counts']
            self.neighbors = graph_data['neighbors']
            self.bond_lengths = graph_data['bond_lengths']
            self.cart_coords = graph_data['cart_coords']


            self.nodes = np.array(self.nodes)
            self.type_counts = np.array(self.type_counts)
            self.neighbor_counts = np.array(self.neighbor_counts)
            self.neighbors = np.array(self.neighbors)
            self.bond_lengths = np.array(self.bond_lengths)
            self.cart_coords = np.array(self.cart_coords)

        except KeyError as e:
            raise ValueError(f"Missing required graph data field: {str(e)}")

        if len(self.nodes) != len(self.cart_coords):
            raise ValueError(f"Number of nodes ({len(self.nodes)}) doesn't match coordinate count ({len(self.cart_coords)})")
        if self.cart_coords.shape[1] != 3:
            raise ValueError("Coordinates must be 3-dimensional")
        if len(self.bond_lengths) != len(self.neighbors):
            raise ValueError(f"Bond lengths count ({len(self.bond_lengths)}) must match neighbor count ({len(self.neighbors)})")

        self.edge_attr = self._create_edge_attributes()
        self.edge_index = self._create_edge_index()

    def _create_edge_attributes(self):
        """Create edge attributes with bond lengths and types"""
        edge_types = []
        for edge_type, count in enumerate(self.type_counts):
            edge_types.extend([edge_type] * count)

        if len(edge_types) != len(self.bond_lengths):
            raise ValueError(f"Edge type count ({len(edge_types)}) doesn't match bond lengths count ({len(self.bond_lengths)})")

        return torch.tensor(
            np.column_stack([self.bond_lengths, edge_types]),
            dtype=torch.float32
        )

    def _create_edge_index(self):
        """Create edge index with source and target nodes"""
        edge_sources = []
        num_edge_labels = len(self.type_counts)
        neighbor_counts = self.neighbor_counts.reshape(num_edge_labels, -1)

        for edge_type in range(num_edge_labels):
            for node_idx, count in enumerate(neighbor_counts[edge_type]):
                edge_sources.extend([node_idx] * count)


        edge_targets = []
        start_idx = 0
        for count in self.type_counts:
            end_idx = start_idx + count
            edge_targets.extend(self.neighbors[start_idx:end_idx])
            start_idx = end_idx

        if len(edge_sources) != len(edge_targets):
            raise ValueError(f"Edge sources count ({len(edge_sources)}) and targets count ({len(edge_targets)}) mismatch")
        if len(edge_sources) != len(self.bond_lengths):
            raise ValueError(f"Edge count ({len(edge_sources)}) doesn't match bond length count ({len(self.bond_lengths)})")

        return torch.tensor([edge_sources, edge_targets], dtype=torch.long).contiguous()

class CartesianGraphDataset(Dataset):
    def __init__(self, path, target_name):
        super().__init__()
        self.path = path
        self.target_name = target_name

        self._load_graph_data()
        self._load_config()
        self._load_targets()

        if len(self.graph_data) != len(self.targets):
            raise ValueError(
                f"Graph count ({len(self.graph_data)}) doesn't match "
                f"target count ({len(self.targets)})"
            )

    def _load_graph_data(self):
        """Load and validate graph data from NPZ file"""
        npz_path = os.path.join(self.path, "BandgapTargets.npz")
        try:
            with np.load(npz_path, allow_pickle=True) as data:
                graph_dict = data['graph_dict'].item()
                self.graph_names = list(graph_dict.keys())
                self.graph_data = []

                for name, graph in graph_dict.items():
                    try:
                        if 'cart_coords' not in graph:
                            raise ValueError(f"Missing cart_coords in graph {name}")
                        self.graph_data.append(Graph(graph))
                    except ValueError as e:
                        print(f"Skipping invalid graph {name}: {str(e)}")
                        continue

                if not self.graph_data:
                    raise ValueError("No valid graphs found in NPZ file")
        except Exception as e:
            raise RuntimeError(f"Error loading graph data: {str(e)}")

    def _load_config(self):
        """Load and validate configuration"""
        config_path = os.path.join(self.path, "BandgapTargets_config.json")
        try:
            with open(config_path) as f:
                config = json.load(f)

            self.atomic_numbers = config["atomic_numbers"]
            self.node_vectors = np.array(config["node_vectors"])
            self.n_node_feat = len(self.node_vectors[0])
            self.pos_dim = config.get("pos_dim", 3)
            self.atomic_to_idx = {num: idx for idx, num in enumerate(self.atomic_numbers)}
            if len(self.atomic_to_idx) != len(self.atomic_numbers):
                raise ValueError("Duplicate atomic numbers in config")

        except Exception as e:
            raise RuntimeError(f"Error loading config: {str(e)}")

    def _load_targets(self):
        targets_path = os.path.join(self.path, "BandgapTargets.csv")
        try:
            df = pd.read_csv(targets_path)
            if self.target_name not in df.columns:
                raise ValueError(f"Target column '{self.target_name}' not found in CSV")

            self.targets = df[self.target_name].values
            if len(self.targets) == 0:
                raise ValueError("No targets found in CSV file")

            self.graph_names = df['mpid'].values.tolist()

        except Exception as e:
            raise RuntimeError(f"Error loading targets: {str(e)}")

    def __len__(self):
        return len(self.graph_data)

    def __getitem__(self, index):
        """Create PyG Data object with all features"""
        graph = self.graph_data[index]


        node_features = np.zeros((len(graph.nodes), self.n_node_feat))
        for i, atomic_num in enumerate(graph.nodes):
            idx = self.atomic_to_idx[atomic_num]
            node_features[i] = self.node_vectors[idx]

        data = Data(
            x=torch.tensor(node_features, dtype=torch.float32),
            edge_index=graph.edge_index,
            edge_attr=graph.edge_attr,
            pos=graph.cart_coords.clone().detach() if isinstance(graph.cart_coords, torch.Tensor)
                else torch.tensor(graph.cart_coords, dtype=torch.float32),
            y=torch.tensor([[self.targets[index]]], dtype=torch.float32),
            material_id=self.graph_names[index]
        )
        return data

class RadialBasisLayer(nn.Module):
    def __init__(self, num_rbf, cutoff):
        super().__init__()
        self.num_rbf = num_rbf
        self.cutoff = cutoff

        self.centers = nn.Parameter(torch.linspace(0, cutoff, num_rbf), requires_grad=False)
        self.widths = nn.Parameter((cutoff / num_rbf) * torch.ones(num_rbf), requires_grad=False)

    def forward(self, dist):
        # Apply cutoff
        dist = dist.clamp(max=self.cutoff)

        dist_expanded = dist.expand(-1, self.num_rbf)
        rbf = torch.exp(-((dist_expanded - self.centers.view(1, -1)) / self.widths.view(1, -1))**2)

        envelope = self._envelope(dist)
        envelope_expanded = envelope.expand_as(rbf)
        return rbf * envelope_expanded

    def _envelope(self, dist):
        return 1 - (dist / self.cutoff)**2


class E3EquivariantCGCNNConv(nn.Module):
    def __init__(self, channels, num_rbf=16, cutoff=10.0, lmax=1):
        super().__init__()
        self.channels = channels
        self.num_rbf = num_rbf
        self.cutoff = cutoff
        self.lmax = lmax

        # Radial basis functions (invariant to rotations)
        self.rbf = RadialBasisLayer(num_rbf, cutoff)

        # Scalar network (invariant)
        self.scalar_mlp = nn.Sequential(
            nn.Linear(channels * 2 + num_rbf, channels),
            nn.SiLU(),
            nn.Linear(channels, channels)
        )

        # Define irreps more carefully
        irreps_in1 = o3.Irreps(f"{channels}x0e + {channels}x1e")
        irreps_in2 = o3.Irreps([(1, (l, 1)) for l in range(lmax + 1)])
        irreps_out = o3.Irreps(f"{channels}x0e + {channels}x1e")

        # Tensor product for equivariant message passing
        self.tp = FullyConnectedTensorProduct(
            irreps_in1=irreps_in1,
            irreps_in2=irreps_in2,
            irreps_out=irreps_out,
            internal_weights=True
        )

        # Scalar activation and gate activation
        self.scalar_act = nn.SiLU()
        self.gate_act = nn.Sigmoid()

    def forward(self, x_scalar, x_vector, edge_index, edge_attr, pos):
        row, col = edge_index

        # Calculate distances (invariant)
        edge_vec = pos[row] - pos[col]
        dist = torch.norm(edge_vec, dim=-1, keepdim=True)

        # Get radial basis functions
        rbf_output = self.rbf(dist)

        # Scalar message computation
        scalar_message_input = torch.cat([
            x_scalar[row],
            x_scalar[col],
            rbf_output
        ], dim=-1)

        scalar_message = self.scalar_mlp(scalar_message_input)

        # Calculate spherical harmonics
        edge_sh = spherical_harmonics(
            list(range(self.lmax + 1)),
            edge_vec / (dist + 1e-8),
            normalize=True
        )

        # Combine scalar and vector features for tensor product
        # Flatten vector features properly
        x_vector_flat = x_vector[row].reshape(x_vector[row].shape[0], -1)
        src_features = torch.cat([x_scalar[row], x_vector_flat], dim=-1)

        # Apply tensor product for equivariant message passing
        message = self.tp(src_features, edge_sh)

        # Split message back into scalar and vector parts
        message_scalar = message[:, :self.channels]
        message_vector = message[:, self.channels:].view(-1, self.channels, 3)

        # Aggregate messages
        scalar_out = scatter(scalar_message, col, dim=0, dim_size=x_scalar.size(0), reduce='add')
        vector_out = scatter(message_vector, col, dim=0, dim_size=x_vector.size(0), reduce='add')

        # Apply activations
        scalar_out = self.scalar_act(scalar_out)

        # Apply gating to vectors (equivariant)
        gates = self.gate_act(scalar_out)
        gated_vectors = vector_out * gates.unsqueeze(-1)

        # Update features with residual connections
        x_scalar_new = x_scalar + scalar_out
        x_vector_new = x_vector + gated_vectors

        return x_scalar_new, x_vector_new


class E3EquivariantPrimaryCapsuleLayer(nn.Module):
    def __init__(self, scalar_features, vector_features, out_caps, caps_dim):
        super().__init__()
        self.out_caps = out_caps
        self.caps_dim = caps_dim
        self.scalar_features = scalar_features
        self.vector_features = vector_features

        # Scalar projection (invariant)
        self.scalar_projection = nn.Linear(scalar_features, out_caps * (caps_dim // 2))

        # Vector projection weights (invariant)
        self.vector_weights = nn.Linear(scalar_features, out_caps * vector_features)

        # Vector to scalar transformation
        self.vector_to_scalar = nn.Linear(vector_features * 3, caps_dim // 2)

    def forward(self, x_scalar, x_vector):
        batch_size = x_scalar.size(0)

        # Project scalar features (invariant)
        scalar_out = self.scalar_projection(x_scalar)
        scalar_out = scalar_out.view(batch_size, self.out_caps, self.caps_dim // 2)

        # Get weights for vector features
        vector_weights = self.vector_weights(x_scalar)
        vector_weights = vector_weights.view(batch_size, self.out_caps, self.vector_features)

        # Process vectors for each capsule
        vector_caps = []
        for i in range(self.out_caps):
            # Weight the vectors - need to properly broadcast dimensions
            # vector_weights[:, i, :] has shape [batch_size, vector_features]
            # x_vector has shape [batch_size, vector_features, 3]
            # We need to expand weights to [batch_size, vector_features, 1] for proper multiplication
            weights_i = vector_weights[:, i, :].unsqueeze(-1)  # [batch_size, vector_features, 1]
            weighted_vectors = weights_i * x_vector  # [batch_size, vector_features, 3]

            # Flatten and convert to scalars
            weighted_flat = weighted_vectors.reshape(batch_size, -1)
            vector_scalars = self.vector_to_scalar(weighted_flat)

            vector_caps.append(vector_scalars)

        # Stack to get [batch_size, out_caps, caps_dim//2]
        vector_caps = torch.stack(vector_caps, dim=1)

        # Concatenate scalar and vector parts
        capsules = torch.cat([scalar_out, vector_caps], dim=2)

        return capsules, x_vector


class E3EquivariantSecondaryCapsuleLayer(nn.Module):
    def __init__(self, in_dim, out_caps, out_dim, routing_iterations=2):
        super().__init__()
        self.out_caps = out_caps
        self.out_dim = out_dim
        self.routing_iterations = routing_iterations
        self.in_dim = in_dim

        # Transform matrices (invariant)
        self.W = nn.Parameter(torch.randn(out_caps, in_dim, out_dim))
        self.bias = nn.Parameter(torch.zeros(out_caps, out_dim))

    def squash(self, tensor, dim=-1):
        squared_norm = (tensor ** 2).sum(dim=dim, keepdim=True)
        scale = squared_norm / (1 + squared_norm)
        return scale * tensor / (torch.sqrt(squared_norm) + 1e-8)

    def forward(self, x, x_vectors, batch):
        batch_size = batch.max().item() + 1
        secondary_capsules = []
        secondary_vectors = []

        for b in range(batch_size):
            mask = (batch == b)
            if mask.sum() == 0:
                continue

            x_b = x[mask]  # [nodes, primary_caps, primary_dim]
            nodes, primary_caps, primary_dim = x_b.size()

            # Prepare prediction vectors
            u_hat = torch.zeros(nodes, primary_caps, self.out_caps, self.out_dim, device=x.device)

            # Transform inputs for each output capsule
            for i in range(self.out_caps):
                u_hat[:, :, i, :] = torch.matmul(x_b, self.W[i])

            # Flatten for routing
            u_hat_flat = u_hat.view(-1, self.out_caps, self.out_dim)
            num_inputs = u_hat_flat.size(0)

            # Initialize routing logits
            b_ij = torch.zeros(num_inputs, self.out_caps, device=x.device)

            # Routing algorithm
            for iteration in range(self.routing_iterations):
                c_ij = F.softmax(b_ij, dim=1)
                c_ij = c_ij.unsqueeze(2)

                # Weighted sum
                s_j = (c_ij * u_hat_flat).sum(dim=0) + self.bias

                # Apply squashing
                v_j = self.squash(s_j, dim=1)

                # Update routing logits
                if iteration < self.routing_iterations - 1:
                    agreement = (u_hat_flat * v_j.unsqueeze(0)).sum(dim=2)
                    b_ij = b_ij + agreement

            # Handle vectors if provided
            if x_vectors is not None:
                # Simple aggregation for vectors
                routed_vectors = torch.zeros(self.out_caps, x_vectors.size(1), 3, device=x.device)
                secondary_vectors.append(routed_vectors.unsqueeze(0))

            secondary_capsules.append(v_j.unsqueeze(0))

        if not secondary_capsules:
            return torch.zeros((batch_size, self.out_caps, self.out_dim), device=x.device), None

        return torch.cat(secondary_capsules, dim=0), torch.cat(secondary_vectors, dim=0) if secondary_vectors else None


class E3LayerNorm(nn.Module):
    """E(3)-equivariant layer normalization"""
    def __init__(self, channels, scalar_only=False):
        super().__init__()
        self.channels = channels
        self.scalar_only = scalar_only

        self.scalar_scale = nn.Parameter(torch.ones(channels))
        self.scalar_bias = nn.Parameter(torch.zeros(channels))

        if not scalar_only:
            self.vector_scale = nn.Parameter(torch.ones(channels))

    def forward(self, x_scalar, x_vector=None):
        # Normalize scalars
        mean = x_scalar.mean(dim=1, keepdim=True)
        var = x_scalar.var(dim=1, keepdim=True, unbiased=False)
        x_scalar = (x_scalar - mean) / (var + 1e-5).sqrt()
        x_scalar = x_scalar * self.scalar_scale.view(1, -1) + self.scalar_bias.view(1, -1)

        # Normalize vectors if provided
        if x_vector is not None and not self.scalar_only:
            vec_norm = torch.norm(x_vector, dim=2, keepdim=True)
            vec_mean = vec_norm.mean(dim=1, keepdim=True)
            vec_var = vec_norm.var(dim=1, keepdim=True, unbiased=False)

            scale = (vec_norm / (vec_var + 1e-5).sqrt()) * self.vector_scale.view(1, -1, 1)
            x_vector = x_vector * scale

        return x_scalar, x_vector


class EncoderGNN(nn.Module):
    def __init__(self, node_features, edge_features, hidden_channels, num_conv_layers):
        super().__init__()
        self.hidden_channels = hidden_channels

        # Node embedding
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features + 3, hidden_channels),  # +3 for positions
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU()
        )

        # Edge embedding
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU()
        )

        # Convolution layers
        self.convs = nn.ModuleList([
            E3EquivariantCGCNNConv(hidden_channels)
            for _ in range(num_conv_layers)
        ])

        # Batch normalization layers
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_channels) for _ in range(num_conv_layers)
        ])

        # Layer normalization for E3 equivariance
        self.layer_norms = nn.ModuleList([
            E3LayerNorm(hidden_channels) for _ in range(num_conv_layers)
        ])

        # Primary capsule layer
        self.primary_caps = E3EquivariantPrimaryCapsuleLayer(
            scalar_features=hidden_channels,
            vector_features=hidden_channels,
            out_caps=8,
            caps_dim=16
        )

        # Secondary capsule layer
        self.secondary_caps = E3EquivariantSecondaryCapsuleLayer(
            in_dim=16,
            out_caps=6,
            out_dim=16,
            routing_iterations=3
        )

    def forward(self, data):
        x, edge_index, edge_attr, pos = data.x, data.edge_index, data.edge_attr, data.pos
        batch = data.batch if hasattr(data, 'batch') else torch.zeros(x.size(0), dtype=torch.long, device=x.device)

        # Embed nodes (concatenate features with positions)
        x = torch.cat([x, pos], dim=1)
        x = self.node_embedding(x)

        # Embed edges
        edge_attr = self.edge_embedding(edge_attr)

        # Initialize scalar and vector features
        x_scalar = x
        # Initialize vectors from positions
        x_vector = pos.unsqueeze(1).repeat(1, self.hidden_channels, 1)

        # Apply convolution layers
        for i, conv in enumerate(self.convs):
            x_scalar_res, x_vector_res = x_scalar, x_vector

            # Apply convolution
            x_scalar, x_vector = conv(x_scalar, x_vector, edge_index, edge_attr, pos)

            # Apply normalization
            x_scalar, x_vector = self.layer_norms[i](x_scalar, x_vector)
            x_scalar = self.batch_norms[i](x_scalar)
            x_scalar = F.relu(x_scalar)

            # Add residual connections (skip first layer)
            if i > 0:
                x_scalar = x_scalar + x_scalar_res
                x_vector = x_vector + x_vector_res

        # Apply capsule layers
        primary_caps, primary_vectors = self.primary_caps(x_scalar, x_vector)
        secondary_caps, secondary_vectors = self.secondary_caps(primary_caps, primary_vectors, batch)

        return secondary_caps


class DecoderGNN(nn.Module):
    def __init__(self, latent_dim, hidden_channels, node_features, edge_features, max_nodes=50, device='cuda'):
        super().__init__()
        self.max_nodes = max_nodes
        self.latent_dim = latent_dim
        self.hidden_channels = hidden_channels
        self.node_features = node_features
        self.edge_features = edge_features
        self.device = device

        # Latent to node features
        self.latent_to_nodes = nn.Sequential(
            nn.Linear(latent_dim + 1, hidden_channels * 2),  # +1 for conditional property
            nn.ReLU(),
            nn.Linear(hidden_channels * 2, hidden_channels),
            nn.ReLU()
        )

        # Node feature generator
        self.node_generator = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, node_features)
        )

        # Position generator
        self.pos_generator = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, 3)
        )

        # Edge existence predictor
        self.edge_predictor = nn.Sequential(
            nn.Linear(hidden_channels * 2, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, 1),
            nn.Sigmoid()
        )

        # Edge feature generator
        self.edge_generator = nn.Sequential(
            nn.Linear(hidden_channels * 2, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, edge_features)
        )

    def forward(self, z, target_property, num_nodes=None):
        batch_size = z.size(0)

        # Condition latent code with target property
        z_cond = torch.cat([z, target_property.view(-1, 1)], dim=1)

        if num_nodes is None:
            num_nodes = self.max_nodes

        node_features_list = []
        pos_list = []
        edge_index_list = []
        edge_attr_list = []

        for b in range(batch_size):
            # Generate node hidden representations
            node_hidden = self.latent_to_nodes(z_cond[b].unsqueeze(0).repeat(num_nodes, 1))

            # Generate node features and positions
            node_features = self.node_generator(node_hidden)
            positions = self.pos_generator(node_hidden)

            node_features_list.append(node_features)
            pos_list.append(positions)

            # Generate edges
            edges = []
            edge_features = []

            for i in range(num_nodes):
                for j in range(num_nodes):
                    if i != j:
                        pair_hidden = torch.cat([node_hidden[i], node_hidden[j]], dim=0)
                        exists_prob = self.edge_predictor(pair_hidden.unsqueeze(0))

                        if exists_prob > 0.5:
                            edges.append([i, j])
                            edge_attr = self.edge_generator(pair_hidden.unsqueeze(0))
                            edge_features.append(edge_attr)

            # Handle case with no edges
            if edges:
                edge_index = torch.tensor(edges, dtype=torch.long).t().to(self.device)
                edge_attr = torch.cat(edge_features, dim=0).to(self.device)
            else:
                # Create dummy edge to avoid empty tensors
                edge_index = torch.tensor([[0], [0]], dtype=torch.long).to(self.device)
                edge_attr = torch.zeros((1, self.edge_features), device=self.device)

            edge_index_list.append(edge_index)
            edge_attr_list.append(edge_attr)

        return node_features_list, pos_list, edge_index_list, edge_attr_list


class CrystalVAE(nn.Module):
    def __init__(self, node_features, edge_features, hidden_channels, latent_dim=32,
                 num_conv_layers=2, kl_weight=0.01, device='cuda'):
        super().__init__()
        self.latent_dim = latent_dim
        self.kl_weight = kl_weight
        self.device = device

        # Encoder
        self.encoder = EncoderGNN(
            node_features=node_features,
            edge_features=edge_features,
            hidden_channels=hidden_channels,
            num_conv_layers=num_conv_layers
        )

        # Latent space projections
        # Secondary capsules output: [batch_size, 6, 16] -> flatten to [batch_size, 96]
        self.mu_projection = nn.Linear(6 * 16, latent_dim)
        self.logvar_projection = nn.Linear(6 * 16, latent_dim)

        # Decoder
        self.decoder = DecoderGNN(
            latent_dim=latent_dim,
            hidden_channels=hidden_channels,
            node_features=node_features,
            edge_features=edge_features,
            device=self.device
        )

        # Property predictor
        self.property_predictor = nn.Sequential(
            nn.Linear(latent_dim, hidden_channels // 2),
            nn.ReLU(),
            nn.Linear(hidden_channels // 2, 1)
        )

    def encode(self, data):
        """Encode graph data to latent space"""
        secondary_caps = self.encoder(data)

        # Flatten capsules for projection
        caps_flat = secondary_caps.view(secondary_caps.size(0), -1)

        # Project to latent space
        mu = self.mu_projection(caps_flat)
        logvar = self.logvar_projection(caps_flat)

        return mu, logvar

    def reparameterize(self, mu, logvar):
        """Reparameterization trick for VAE"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z, target_property, num_nodes=None):
        """Decode latent representation to graph structure"""
        return self.decoder(z, target_property, num_nodes)

    def forward(self, data, target_property=None):
        """Forward pass through the VAE"""
        # Encode
        mu, logvar = self.encode(data)

        # Sample from latent space
        z = self.reparameterize(mu, logvar)

        # Predict property if not provided
        if target_property is None:
            pred_property = self.property_predictor(z)
        else:
            pred_property = target_property

        # Decode
        node_features, positions, edge_indices, edge_attrs = self.decode(z, pred_property)

        return {
            'node_features': node_features,
            'positions': positions,
            'edge_indices': edge_indices,
            'edge_attrs': edge_attrs,
            'pred_property': pred_property,
            'mu': mu,
            'logvar': logvar,
            'z': z
        }




    def loss_function(self, data, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar, true_property=None):
    #def loss_function(self, recon_data, original_data, mu, logvar, pred_property=None, true_property=None):
        """Compute VAE loss with reconstruction and KL divergence terms"""


        recon_loss = 0.0
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())

        # Property prediction loss (if available)
        property_loss = 0.0
        if pred_property is not None and true_property is not None:
            property_loss = F.mse_loss(pred_property.squeeze(), true_property)
        elif pred_property is not None and hasattr(data, 'y'):
            property_loss = F.mse_loss(pred_property.squeeze(), data.y.squeeze())



        # Total loss
        total_loss = recon_loss + self.kl_weight * kl_loss + property_loss


        return total_loss, {
            #'recon_loss': recon_loss,
            'kl_loss': kl_loss,
            #'property_loss': property_loss.item() if isinstance(property_loss, torch.Tensor) else property_loss,
            'node_recon': 0.0,
            'pos_recon': 0.0,
            'edge_recon': 0.0,
            'prop_loss': property_loss.item() if isinstance(property_loss, torch.Tensor) else property_loss

        }

    def generate(self, num_samples, target_property, num_nodes=None, device=None):
        """Generate new crystal structures"""
        if device is None:
            device = self.device


        z = torch.randn(num_samples, self.latent_dim, device=device)

        if not isinstance(target_property, torch.Tensor):
            target_property = torch.tensor([target_property] * num_samples, device=device)

        # Decode
        with torch.no_grad():
            node_features, positions, edge_indices, edge_attrs = self.decode(z, target_property, num_nodes)

        return node_features, positions, edge_indices, edge_attrs




def run_crystal_vae(dataset_path, target_name, epochs=100, batch_size=32,
                   hidden_channels=128, latent_dim=64, num_conv_layers=3,
                   kl_weight=0.01, learning_rate=1e-4, pretrained_model_path=None):
    """Main function to run crystal VAE training"""

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Create output directory
    output_dir = f"crystal_vae_{target_name}"
    os.makedirs(output_dir, exist_ok=True)
    checkpoint_path = os.path.join(output_dir, f"best_crystal_vae_{target_name}.pt")

    print("=" * 50)
    print(f"Crystal VAE Training Configuration:")
    print(f"- Target property: {target_name}")
    print(f"- Epochs: {epochs}")
    print(f"- Batch size: {batch_size}")
    print(f"- Hidden channels: {hidden_channels}")
    print(f"- Latent dimension: {latent_dim}")
    print(f"- Conv layers: {num_conv_layers}")
    print(f"- KL weight: {kl_weight}")
    print(f"- Learning rate: {learning_rate}")
    print(f"- Device: {device}")
    print(f"- Output directory: {output_dir}")
    print("=" * 50)

    # Load dataset - you'll need to import your dataset class here
    print("Loading dataset...")
    dataset = CartesianGraphDataset(dataset_path, target_name=target_name)
    print(f"Dataset loaded with {len(dataset)} samples")

    # Split dataset
    train_size = int(0.8 * len(dataset))
    val_size = int(0.1 * len(dataset))
    test_size = len(dataset) - train_size - val_size

    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size]
    )

    print(f"Split: {train_size} train, {val_size} validation, {test_size} test")

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        collate_fn=Batch.from_data_list
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        num_workers=4,
        pin_memory=True,
        collate_fn=Batch.from_data_list
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        num_workers=4,
        pin_memory=True,
        collate_fn=Batch.from_data_list
    )

    # Initialize model
    print("Initializing Crystal VAE model")
    model = CrystalVAE(
        node_features=dataset[0].x.size(1),
        edge_features=dataset[0].edge_attr.size(1),
        hidden_channels=hidden_channels,
        latent_dim=latent_dim,
        num_conv_layers=num_conv_layers,
        kl_weight=kl_weight,
        device=device
    ).to(device)

    print(f"Model parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Load pretrained model if provided
    if pretrained_model_path and os.path.exists(pretrained_model_path):
        print(f"Loading pretrained model from {pretrained_model_path}")
        checkpoint = torch.load(pretrained_model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("Pretrained model loaded successfully.")

    # Setup optimizer and scheduler
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )

    # Train model
    print(f"Starting training for {epochs} epochs...")
    model, history = train_crystal_vae(
        model, train_loader, val_loader, optimizer, device,
        epochs=epochs,
        scheduler=scheduler,
        checkpoint_path=checkpoint_path,
        early_stopping_patience=20
    )

    # Save training history
    np.savez(os.path.join(output_dir, "training_history.npz"), **history)
    print(f"✓ Saved training history to {output_dir}/training_history.npz")

    # Evaluate on test set
    print("Evaluating model on test set...")
    model.eval()
    test_loss = 0
    property_errors = []

    with torch.no_grad():
        for batch in test_loader:
            batch = batch.to(device)
            output = model(batch)

            node_features = output['node_features']
            positions = output['positions']
            edge_indices = output['edge_indices']
            edge_attrs = output['edge_attrs']
            pred_property = output['pred_property']
            mu = output['mu']
            logvar = output['logvar']

            loss, _ = model.loss_function(
                batch, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar
            )
            test_loss += loss.item()

            property_errors.append((pred_property - batch.y).abs().cpu().numpy())

    avg_test_loss = test_loss / len(test_loader)
    property_errors = np.concatenate(property_errors)
    mean_abs_error = property_errors.mean()

    print(f"Test Loss: {avg_test_loss:.4f}")
    print(f"Mean Absolute Error for {target_name}: {mean_abs_error:.4f}")

    # Save test results
    with open(os.path.join(output_dir, "test_results.txt"), "w") as f:
        f.write(f"Test Loss: {avg_test_loss:.6f}\n")
        f.write(f"Mean Absolute Error for {target_name}: {mean_abs_error:.6f}\n")

    print(f"Model training complete. You can now use this model to generate crystal structures with specific {target_name} values.")
    print(f"To generate structures, load the saved model from {checkpoint_path} and use the model.generate() method.")

    return model, history


def generate_crystals(model_path, target_values, num_samples=5, device=None):
    """Generate crystal structures with specific target properties"""

    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Load model
    checkpoint = torch.load(model_path, map_location=device)
    model_config = checkpoint['model_config']

    model = CrystalVAE(
        node_features=model_config['node_features'],
        edge_features=model_config['edge_features'],
        hidden_channels=model_config['hidden_channels'],
        latent_dim=model_config['latent_dim'],
        device=device
    ).to(device)

    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    generated_structures = {}

    for target in target_values:
        print(f"Generating {num_samples} structures with target property {target}...")
        generated_graphs = model.generate(
            target_property=target,
            num_samples=num_samples,
            device=device
        )
        generated_structures[target] = generated_graphs
        print(f"Generated {len(generated_graphs)} structures with target property {target}")

    return generated_structures


def train_crystal_vae(model, train_loader, val_loader, optimizer, device, epochs=100,
                     scheduler=None, checkpoint_path="best_vae_model.pt",
                     early_stopping_patience=20):
    model.train()
    best_loss = float('inf')
    patience_counter = 0

    history = {
        'epoch': [],
        'train_loss': [],
        'val_loss': [],
        'node_recon': [],
        'pos_recon': [],
        'edge_recon': [],
        'prop_loss': [],
        'kl_loss': []
    }

    checkpoint_dir = os.path.dirname(checkpoint_path)
    os.makedirs(checkpoint_dir, exist_ok=True)
    log_path = os.path.join(checkpoint_dir, "training_log.csv")

    print(f"Training on {device} for {epochs} epochs")
    print(f"Checkpoints will be saved to {checkpoint_path}")

    for epoch in range(epochs):
        model.train()
        epoch_loss = 0

        loss_components = {
            'node_recon': 0,
            'pos_recon': 0,
            'edge_recon': 0,
            'prop_loss': 0,
            'kl_loss': 0
        }

        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} [Train]")

        for batch in progress_bar:
            batch = batch.to(device)
            optimizer.zero_grad()

            # Forward pass - model returns a dictionary
            output = model(batch)

            # Extract outputs from dictionary
            node_features = output['node_features']
            positions = output['positions']
            edge_indices = output['edge_indices']
            edge_attrs = output['edge_attrs']
            pred_property = output['pred_property']
            mu = output['mu']
            logvar = output['logvar']

            # Compute loss
            loss, components = model.loss_function(
                batch, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar
            )

            loss.backward()
            optimizer.step()

            progress_bar.set_postfix(loss=f"{loss.item():.4f}")

            epoch_loss += loss.item()
            for k, v in components.items():
                loss_components[k] += v

        avg_train_loss = epoch_loss / len(train_loader)
        for k in loss_components:
            loss_components[k] /= len(train_loader)

        # Validation
        model.eval()
        val_loss = 0
        progress_bar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} [Val]")

        with torch.no_grad():
            for batch in progress_bar:
                batch = batch.to(device)

                # Forward pass
                output = model(batch)

                # Extract outputs
                node_features = output['node_features']
                positions = output['positions']
                edge_indices = output['edge_indices']
                edge_attrs = output['edge_attrs']
                pred_property = output['pred_property']
                mu = output['mu']
                logvar = output['logvar']

                loss, _ = model.loss_function(
                    batch, node_features, positions, edge_indices, edge_attrs, pred_property, mu, logvar
                )

                val_loss += loss.item()
                progress_bar.set_postfix(loss=f"{loss.item():.4f}")

        avg_val_loss = val_loss / len(val_loader)

        # Update history
        history['epoch'].append(epoch+1)
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        for k in loss_components:
            history[k].append(loss_components[k])

        print(f"[Epoch {epoch+1}/{epochs}] Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f} | "
              f"Node: {loss_components['node_recon']:.4f}, "
              f"Pos: {loss_components['pos_recon']:.4f}, "
              f"Prop: {loss_components['prop_loss']:.4f}, "
              f"KL: {loss_components['kl_loss']:.4f}")

        # Save log header on first epoch
        if epoch == 0:
            with open(log_path, 'w') as f:
                f.write(','.join(['epoch', 'train_loss', 'val_loss'] + list(loss_components.keys())) + '\n')

        # Append to log
        with open(log_path, 'a') as f:
            f.write(','.join([str(epoch+1), f"{avg_train_loss:.6f}", f"{avg_val_loss:.6f}"] +
                            [f"{loss_components[k]:.6f}" for k in loss_components]) + '\n')

        # Learning rate scheduling
        if scheduler:
            scheduler.step(avg_val_loss)

        # Early stopping and checkpointing
        if avg_val_loss < best_loss:
            best_loss = avg_val_loss
            patience_counter = 0

            # Save best model
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_train_loss,
                'val_loss': best_loss,
                'history': history,
                'model_config': {
                    'node_features': model.decoder.node_features,
                    'edge_features': model.decoder.edge_features,
                    'hidden_channels': model.decoder.hidden_channels,
                    'latent_dim': model.latent_dim,
                }
            }, checkpoint_path)
            print(f"✓ Saved best model at epoch {epoch+1} (val_loss: {best_loss:.4f})")

            # Backup every 10 epochs
            if (epoch + 1) % 10 == 0:
                backup_path = checkpoint_path.replace('.pt', f'_epoch{epoch+1}.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': avg_train_loss,
                    'val_loss': avg_val_loss,
                    'history': history,
                    'model_config': {
                        'node_features': model.decoder.node_features,
                        'edge_features': model.decoder.edge_features,
                        'hidden_channels': model.decoder.hidden_channels,
                        'latent_dim': model.latent_dim,
                    }
                }, backup_path)
        else:
            patience_counter += 1
            if patience_counter >= early_stopping_patience:
                print(f"Early stopping triggered after {patience_counter} epochs without improvement")
                break

        # Plot training curves every 10 epochs
        if (epoch + 1) % 10 == 0 or epoch == epochs - 1:
            try:
                import matplotlib.pyplot as plt
                plot_training_curves(history, checkpoint_dir, epoch+1)
                print(f"✓ Saved loss plot at epoch {epoch+1}")
            except Exception as e:
                print(f"Could not generate loss plot: {str(e)}")

    return model, history


def plot_training_curves(history, output_dir, epoch):
    """Plot training curves"""
    import matplotlib.pyplot as plt

    plt.figure(figsize=(15, 10))

    # Plot train and validation loss
    plt.subplot(2, 2, 1)
    plt.plot(history['epoch'], history['train_loss'], 'b-', label='Train Loss')
    plt.plot(history['epoch'], history['val_loss'], 'r-', label='Validation Loss')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot property prediction loss
    plt.subplot(2, 2, 2)
    plt.plot(history['epoch'], history['prop_loss'], 'g-', label='Property Prediction Loss')
    plt.title('Property Prediction Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot reconstruction losses
    plt.subplot(2, 2, 3)
    plt.plot(history['epoch'], history['node_recon'], label='Node Reconstruction')
    plt.plot(history['epoch'], history['pos_recon'], label='Position Reconstruction')
    plt.plot(history['epoch'], history['edge_recon'], label='Edge Reconstruction')
    plt.title('Reconstruction Losses')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    # Plot KL loss
    plt.subplot(2, 2, 4)
    plt.plot(history['epoch'], history['kl_loss'], 'm-', label='KL Divergence')
    plt.title('KL Divergence Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"loss_plot_epoch_{epoch}.png"))
    plt.close()


if __name__ == "__main__":
    # Example usage - replace with your actual dataset path and parameters
    dataset_path = "/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/MaterialsProjectData/20KSPLIT/"

    model, history = run_crystal_vae(
        dataset_path=dataset_path,
        target_name="e_form",
        epochs=1,  # Set to desired number of epochs
        batch_size=16,
        hidden_channels=128,
        latent_dim=64,
        num_conv_layers=2,
        kl_weight=0.01,
        learning_rate=1e-4
    )